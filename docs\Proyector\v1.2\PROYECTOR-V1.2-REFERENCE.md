# Proyector v1.2 - Referencia General del Sistema

**ID**: proyector-v1.2-reference
**Versión**: 1.2
**Fecha**: Enero 2025
**Estado**: 📚 **REFERENCIA COMPLETA** - Documento maestro para agentes AI sobre arquitectura Proyector v1.2

## 🎯 **RESUMEN EJECUTIVO**

El Proyector v1.2 es un **ecosistema completamente integrado** basado en la arquitectura **Sites-CodeBlocks** que permite crear, gestionar y evolucionar sitios web inteligentes de forma completamente dinámica. Todo el sistema se basa en el principio fundamental: **TODO ES SPECS ↔ CODEBLOCKS**.

## 🏗️ **ARQUITECTURA FUNDAMENTAL**

### **Principio Central: Sites-CodeBlocks-Specs**
```
PROYECTOR = SITES[configurables] + CODEBLOCKS[funcionalidad] + SPECS[metadatos]
```

- **Sites**: Espacios de trabajo independientes multi-tenant
- **CodeBlocks**: Funcionalidad ejecutable (static/dynamic)
- **Specs**: Metadatos que referencian CodeBlocks para 4 propósitos:
  1. 🎯 **Orquestación de Procesos** - Workflows, automatizaciones
  2. 🎨 **Renderizado de UI** - Componentes, layouts dinámicos
  3. 💾 **Almacenamiento de Datos** - Validaciones, transformaciones
  4. ⚙️ **Configuración Dinámica** - Settings, personalizaciones

### **Separación Backend/Frontend Estricta**
- **Backend**: `functions-js/` - NUNCA importa desde `public/` o `src/`
- **Frontend**: `src/` - Usa HyperContext para datos
- **Static Resources**: `public/staticModules/` y `public/staticCodeblocks/`
- **Intermediario**: HyperContext en Firestore como única fuente de verdad

## 🌐 **HYPERCONTEXT: NOMENCLATURA Y PATHS**

### **Diferencias Críticas: HyperContext vs Firebase**

#### **HyperContext Paths (Notación de Puntos)**
```javascript
// ✅ CORRECTO: HyperContext notation
"sites[siteId].entities[entityType][entityId]"
"users[userId].profile"
"sites[main].specs[specId]"
```

#### **Firebase Paths (Estructura Real)**
```javascript
// Estructura Firestore resultante:
// HyperContext_dev/sites/siteId/entities/entityType/entityId
// HyperContext_dev/users/userId/profile
// HyperContext_dev/sites/main/specs/specId
```

### **Prefijos Automáticos**
- **Producción**: `HyperContext`
- **Desarrollo/Híbrido**: `HyperContext_dev`

### **Tipos de Datos por Estructura**
- **📄 Documento Base**: < 100KB - Datos principales
- **📋 Documento Individual**: < 500KB - Extensión específica  
- **📚 Subcollection**: Ilimitado - Datos dinámicos
- **🔧 Spec**: Metadato que referencia CodeBlocks
- **🧩 CodeBlock**: Funcionalidad ejecutable

## 🏢 **ARQUITECTURA DE SITES**

### **Estructura Site-Specific (NO Global)**
```javascript
sites[siteId] = {
  // Configuración básica
  name: "Mi Empresa",
  slug: "mi-empresa", // URL: mi-empresa.proyector.app
  
  // Plan y límites
  plan: {
    id: "professional",
    limits: { storage: "10GB", aiTokens: 100000, users: 5 },
    usage: { storage: "2.5GB", aiTokens: 15000, users: 2 }
  },
  
  // Branding personalizable
  branding: {
    colors: { primary: "#1976d2", secondary: "#dc004e" },
    typography: { headings: "Roboto", body: "Open Sans" },
    assets: { logo: "sites/siteId/assets/logo.svg" }
  },
  
  // Módulos habilitados según plan
  modules: {
    pages: { enabled: true, config: { enableSEO: true } },
    contacts: { enabled: true, config: { enableCRM: true } }
  }
}

// Subcollections del site
sites[siteId].specs[]        // 📚 Metadatos con referencias CodeBlocks
sites[siteId].codeblocks[]   // 📚 Funcionalidad ejecutable
sites[siteId].entities[]     // 📚 Datos del site
sites[siteId].pages[]        // 📚 Contenido del site
```

### **Site 'main' = Global**
- El site `main` actúa como **global** porque es el host site
- Configuraciones del sistema se almacenan en `sites[main].*`
- **NO existe configuración global fuera de sites**

## 🧩 **SISTEMA CODEBLOCKS**

### **Tipos de CodeBlocks**

#### **Static CodeBlocks** (`public/staticCodeblocks/`)
- **Propósito**: Funcionalidades core del sistema
- **Gestión**: Mantenidos por equipo Proyector
- **Ejemplos**: `cart-widget`, `analytics-dashboard`, `entity-form-block`
- **Estructura**:
  ```
  public/staticCodeblocks/[blockId]/
  ├── codeblock.json    # Configuración principal
  ├── component.jsx     # Componente React
  ├── server.js         # Lógica backend (opcional)
  ├── styles.css        # Estilos (opcional)
  └── template.json     # Template JSON (opcional)
  ```

#### **Dynamic CodeBlocks** (HyperContext)
- **Propósito**: Funcionalidades custom por usuario
- **Gestión**: Creados via BotAgents/chat
- **Storage**: `sites[siteId].dynamicCodeblocks[]`
- **Marketplace**: Compartir entre usuarios

### **Static Modules** (`public/staticModules/`)
- **Propósito**: Configuraciones JSON + Specs
- **Ejemplos**: `contacts`, `sales`, `pages`, `analytics`
- **Estructura**:
  ```
  public/staticModules/[moduleId]/
  ├── module.json       # Configuración principal
  ├── entities.json     # Definiciones de entidades
  ├── pages.json        # Páginas del módulo
  ├── aiTools.json      # Herramientas AI (opcional)
  └── specs.json        # Especificaciones (opcional)
  ```

## 🎨 **PAGEBLOCKS UNIVERSAL ENGINE**

### **Arquitectura JSON-Driven**
- **Principio**: Eliminar componentes React hardcodeados
- **Separación**: Templates JSON + Controllers JavaScript
- **Renderizado**: UniversalRenderer convierte JSON → React

### **Componentes Clave**
```javascript
// UniversalRenderer.jsx - Motor principal
src/proyector/pageBlocks/renderers/UniversalRenderer.jsx

// ComponentFactory.js - Factory de componentes
src/proyector/pageBlocks/universal/ComponentFactory.js

// TemplateLibrary.js - Biblioteca templates
src/proyector/pageBlocks/universal/TemplateLibrary.js

// ControllerManager.js - Gestión controllers
src/proyector/pageBlocks/universal/ControllerManager.js
```

### **Resolución Jerárquica**
1. **Static**: Templates JSON estáticos
2. **Dynamic**: Módulos dinámicos desde HyperContext
3. **Site-specific**: Overrides específicos por site
4. **Fallback**: Automático a static si dynamic falla

## 🧠 **SISTEMA AI Y ZUSTAND**

### **Store Unificado** (`src/proyector/store/`)
- **Arquitectura**: Zustand con Firebase real-time sync
- **Slices principales**: `authSlice`, `sitesSlice`, `entitiesSlice`, `formsSlice`
- **Middleware**: `firebaseSyncMiddleware`, `persistenceMiddleware`

### **Chat Lateral** (`src/proyector/store/useChat.js`)
- **Propósito**: Interface principal del Proyector
- **Herramientas**: Dinámicas según contexto del site
- **AI Integration**: BotAgents con memoria agéntica

### **BotAgents** (`functions-js/proyector/ai/`)
- **Funcionalidad**: Generación de código dinámico
- **Flujos**: Graph-based con ReactFlow UI
- **Memoria**: Sistema de contexto y aprendizaje

## 🔐 **AUTENTICACIÓN Y PERMISOS**

### **Sistema de Usuarios**
```javascript
// Usuario global (único dato global)
users[userId] = {
  publicProfile: { name, avatar },
  privateData: { email, preferences },
  metadata: { createdAt, lastLogin }
}

// Perfil específico por site
sites[siteId].profiles[userId] = { role, permissions, siteSpecificData }

// Credenciales por site
sites[siteId].credentials[userId] = { permissions, accessLevel }
```

### **Permisos Granulares**
- **Niveles**: public, site_member, admin, self
- **Recursos**: static/dynamic modules, codeblocks, entities
- **Validación**: GranularPermissionSystem con cache inteligente

## 📁 **ESTRUCTURA DE ARCHIVOS CLAVE**

### **⚠️ DELIMITACIÓN CRÍTICA: PROYECTOR vs LEGACY**

#### **✅ PROYECTOR V1.2 (Arquitectura Actual)**
```
src/proyector/                   # Frontend Proyector v1.2
├── components/                  # Componentes React modernos
├── store/                       # Zustand store unificado
├── pageBlocks/                  # Universal Engine
├── hooks/                       # Hooks personalizados
└── routes/                      # Sistema de rutas

functions-js/proyector/          # Backend Proyector v1.2
├── HyperContext.js              # Capa base abstracción Firebase
├── sites/                       # Gestión de sites
├── specs/                       # Sistema specs dinámicos
└── security/                    # Permisos granulares

public/staticModules/            # Módulos estáticos v1.2
public/staticCodeblocks/         # CodeBlocks estáticos v1.2
public/staticSites/              # Sites de demostración v1.2
```

#### **❌ LEGACY (NO TOCAR - Fuera de Proyector)**
```
src/modules/                     # LEGACY - Sistema antiguo
src/components/                  # LEGACY - Componentes antiguos
src/pages/                       # LEGACY - Páginas antiguas
public/dataInstances/            # LEGACY - Datos antiguos
public/data/                     # LEGACY - Configuraciones antiguas
functions-js/index.js (exports)  # LEGACY - Solo mantener Proyector exports
```

### **Backend Proyector** (`functions-js/proyector/`)
```
├── HyperContext.js              # Capa base abstracción Firebase
├── HyperContextEnhanced.js      # Navegación jerárquica sites/entities
├── SystemInitializer.js        # Inicialización del sistema
├── UserManager.js               # Gestión de usuarios
├── specs/DynamicSpecsManager.js # Gestión specs dinámicos
├── sites/SiteCreator.js         # Creación de sites
├── security/GranularPermissionSystem.js # Sistema permisos
└── dynamic/                     # Sistema dinámico
    ├── DynamicCodeStorage.js    # Storage código dinámico
    ├── BundleGenerator.js       # Generación bundles
    └── LibraryManagerBackend.js # Gestión librerías
```

### **Frontend Proyector** (`src/proyector/`)
```
├── libs/HyperContextFrontend.js # Cliente HyperContext
├── store/index.js               # Store Zustand unificado
├── hooks/useHyperContext.js     # Hook principal datos
├── pageBlocks/renderers/UniversalRenderer.jsx # Motor renderizado
├── dynamic/                     # Sistema carga dinámica
│   ├── BundleLoader.js          # Cargador bundles
│   ├── ModuleRegistry.js        # Registro módulos
│   └── LibraryManager.js        # Gestión librerías frontend
└── utils/PathConstructor.js     # Construcción paths
```

## 🔗 **DOCUMENTACIÓN DE REFERENCIA**

### **Documentos Principales v1.2**
- **[README.md](./README.md)** - Índice completo documentación
- **[big-picture-unified-v1.2.md](./big-picture-unified-v1.2.md)** - Arquitectura holística
- **[tree-file-v1.2.md](./tree-file-v1.2.md)** - Estado actual implementación
- **[complete-system-diagrams.md](./complete-system-diagrams.md)** - Diagramas exhaustivos
- **[sites-architecture-detailed.md](./sites-architecture-detailed.md)** - Arquitectura sites
- **[SPECS-CODEBLOCKS-FINAL.md](./SPECS-CODEBLOCKS-FINAL.md)** - Arquitectura specs-codeblocks

### **Arquitectura y Paths**
- **[big-picture/hypercontext-paths-tree.md](./big-picture/hypercontext-paths-tree.md)** - Paths HyperContext
- **[architecture/backend-frontend-separation.md](./architecture/backend-frontend-separation.md)** - Separación backend/frontend

## ⚡ **COMANDOS Y DESARROLLO**

### **Comandos Principales**
```bash
# Desarrollo
npm run dev          # Frontend + Firebase emulators
npm run build        # Build producción
npm run test         # Tests completos

# Firebase
firebase emulators:start  # Solo emulators
firebase deploy          # Deploy producción
```

### **Metodología de Desarrollo**
1. **Investigación exhaustiva** antes de implementación
2. **Lectura completa de archivos** para debugging
3. **Verificación arquitectural** antes de cambios
4. **Actualización documentación** en cada cambio

## 🚀 **SISTEMA DINÁMICO IMPLEMENTADO**

### **Dynamic System Architecture**
```
DYNAMIC SYSTEM = BOTAGENTS GENERATION + HYPERCONTEXT STORAGE + INTELLIGENT CACHING + FRONTEND LOADING
```

### **Flujo Completo Dynamic System**
1. **Usuario**: "Crear módulo inventario" via chat
2. **BotAgents**: Genera código dinámico
3. **HyperContext**: Almacena en `sites[siteId].dynamicModules[]`
4. **Bundle System**: Comprime y optimiza código
5. **Frontend**: Carga y ejecuta dinámicamente

### **Storage Strategy por Entorno**
```javascript
// Development: HyperContext Firestore (raw code)
sites[siteId].dynamicModules[moduleId] = {
  code: "raw_javascript_code",
  metadata: { version, dependencies },
  status: "development"
}

// Staging: HyperContext bundled
sites[siteId].dynamicModules[moduleId] = {
  code: "raw_code",
  bundle: "compressed_bundle",
  status: "staging"
}

// Production: Firebase Storage reference
sites[siteId].cachedBundles[moduleId] = {
  storagePath: "sites/siteId/dynamic-modules/moduleId.gz",
  compressionRatio: 0.25,
  status: "production"
}
```

### **Frontend Dynamic Loading**
- **BundleLoader**: Cache-first strategy con TTL
- **Decompressor**: Browser-native APIs (gzip/brotli)
- **ModuleRegistry**: Namespacing y hot reload
- **ExecutionSlots**: Contextos aislados por tipo
- **LibraryManager**: Resolución automática React/Zustand/etc

## 🔧 **CORRECCIONES CRÍTICAS IMPLEMENTADAS**

### **Separación Backend/Frontend (2025-07-02)**
- ✅ **ProyectorV1Handler.js**: Eliminadas importaciones a `public/staticCodeblocks`
- ✅ **ModuleManager.js**: Reemplazado acceso directo con HyperContext
- ✅ **SystemIndexer.js**: Eliminadas referencias hardcodeadas
- ✅ **Patrón Establecido**: Backend NUNCA importa desde `public/` o `src/`

### **Errores Críticos Resueltos (2025-07-01)**
- ✅ **ChartConfigDataField**: Referencias estáticas corregidas
- ✅ **Store Index**: Export duplicado eliminado
- ✅ **LibraryManager**: Dependencias faltantes instaladas
- ✅ **Ionic Components**: Componentes deprecados removidos
- ✅ **Iconicons**: Iconos faltantes corregidos

### **Migración Modules Completada (2025-06-28)**
- ✅ **Renombrado**: `public/modules/` → `public/staticModules/`
- ✅ **Migrado**: Componentes React → `public/staticCodeblocks/`
- ✅ **Eliminado**: `src/proyector/modules/` completamente
- ✅ **17 Static Modules**: Sistema completo migrado
- ✅ **12 Static CodeBlocks**: Componentes React migrados

## 📊 **MÉTRICAS DEL SISTEMA**

### **Arquitectura Implementada**
- **100%** Separación backend/frontend
- **100%** Arquitectura JSON-driven
- **62** Funcionalidades en 7 slices Zustand
- **50+** Componentes migrados a arquitectura correcta
- **17** Static Modules configurados
- **12** Static CodeBlocks implementados

### **Performance y Escalabilidad**
- **Cache multinivel**: Operaciones, estado, principal
- **Compresión adaptativa**: gzip/brotli por entorno
- **Bundle optimization**: Categorías core/ui/business/site-specific
- **Real-time sync**: Firebase onSnapshot patterns
- **Límite 1MB**: Manejado con subcollections automáticas

## 🎯 **METODOLOGÍA OPERATIVA**

### **"Operativo Completar Checks"**
1. **Investigación exhaustiva** antes de implementación
2. **Lectura completa de archivos** para debugging
3. **Verificación arquitectural** antes de cambios
4. **Validación cross-component** para alineación
5. **Actualización documentación** progresiva

### **Metodología para Tasks/Subtasks Robustos**

#### **Fase 1: Investigación Arquitectural (SI O SI)**
```markdown
1. **Delimitar Scope Proyector v1.2**:
   - ✅ INCLUIR: src/proyector/, public/staticModules/, public/staticCodeblocks/, public/staticSites/
   - ❌ EXCLUIR: public/dataInstances/, public/data/, src/modules/, src/components/, src/pages/

2. **Investigar Componentes Existentes**:
   - Leer archivos completos relacionados al task
   - Identificar patrones arquitecturales actuales
   - Verificar alineación con Sites-CodeBlocks-Specs

3. **Verificar Reutilización**:
   - Buscar componentes existentes que puedan reutilizarse
   - Evaluar si la solución ya existe parcialmente
   - Identificar gaps específicos a resolver
```

#### **Fase 2: Planificación Detallada**
```markdown
1. **Definir Objetivo Específico**:
   - Resultado esperado concreto y medible
   - Criterios de éxito claros
   - Impacto en arquitectura general

2. **Diseñar Solución Alineada**:
   - Usar patrones Proyector v1.2 existentes
   - Mantener separación backend/frontend estricta
   - Aplicar principio Sites-CodeBlocks-Specs

3. **Identificar Archivos a Modificar**:
   - Lista específica de archivos Proyector v1.2
   - Orden de modificación (dependencias)
   - Puntos de integración críticos
```

#### **Fase 3: Implementación Progresiva**
```markdown
1. **Implementar por Componentes**:
   - Un componente a la vez
   - Verificar funcionamiento antes de continuar
   - Mantener coherencia arquitectural

2. **Validar Integración**:
   - Probar interacción entre componentes
   - Verificar no romper funcionalidad existente
   - Confirmar alineación con big-picture

3. **Actualizar Documentación**:
   - Registrar cambios en tree-file-v1.2.md
   - Actualizar diagramas si aplica
   - Documentar nuevos patrones creados
```

#### **Fase 4: Verificación y Checks**
```markdown
1. **Verificación Técnica**:
   - Funcionamiento correcto de la implementación
   - No errores de sintaxis o runtime
   - Performance adecuado

2. **Verificación Arquitectural**:
   - Alineación con principios Proyector v1.2
   - Separación backend/frontend mantenida
   - Patrones Sites-CodeBlocks-Specs respetados

3. **Marcar Task Completo**:
   - Solo cuando TODOS los criterios se cumplen
   - Documentación actualizada
   - Integración verificada
```

### **Principios de Desarrollo**
- **SI O SI**: Verificación exhaustiva antes de implementar
- **Frontend-First**: HyperContextFrontend para todas las operaciones
- **JSON-Driven**: DataFields, FieldSets, template.json para UI
- **Separación Estricta**: Controllers puros + Templates JSON
- **Sites-Specific**: Todo es site-specific, 'main' = global
- **Solo Proyector v1.2**: NUNCA tocar archivos legacy (dataInstances, data, src/modules)

## 🔮 **PRÓXIMOS PASOS ARQUITECTURALES**

### **Fase Actual: Dynamic System Integration**
- **BotAgents Code Generation**: Sistema generación via chat
- **Intelligent Caching**: Bundling y compresión producción
- **Dynamic Permissions**: Permisos específicos recursos dinámicos
- **Marketplace Integration**: Compartir CodeBlocks entre usuarios

### **Visión v1.3**
- **Multi-tenant Global**: Millones de sites simultáneos
- **AI Autónomo**: Agentes que crean sites automáticamente
- **Blockchain Integration**: NFTs, smart contracts, Web3
- **Metaverse Ready**: Sites en realidad virtual/aumentada

---

**Este documento sirve como referencia completa para agentes AI trabajando con el Proyector v1.2. Debe leerse al inicio de cada conversación nueva para entender la arquitectura completa del sistema.**
