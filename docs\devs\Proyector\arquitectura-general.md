# Arquitectura General del Proyector

## Resumen Ejecutivo

El **Proyector** es un sistema completo e independiente que coexiste con el sistema legacy de Harmony, implementando una arquitectura moderna basada en:

- **Store Unificado** (Zustand) para gestión centralizada de estado
- **Sistema PageBlocks** declarativo para definición de páginas
- **<PERSON><PERSON><PERSON><PERSON>** con carga desde `/public/modules/`
- **Instancias Estáticas** para demostración y desarrollo de UI

## Objetivos Principales

### 1. **Robustecimiento de UI**
- Navegación fluida entre instancias estáticas
- Renderizado correcto de módulos de prueba
- Visualización de rutas estáticas definidas en módulos
- **NO** persistencia de datos (solo UI)
- **NO** autenticación real (solo mockups)
- **NO** backend integration (solo frontend)

### 2. **Coexistencia Total**
- Sistema completamente independiente en paralelo
- Rutas con prefijo `/new/` para evitar conflictos
- Compatibilidad 100% con backend actual
- Sin interferencia con sistema legacy

### 3. **Arquitectura Moderna**
- Store unificado vs estado distribuido legacy
- PageBlocks declarativos vs componentes hardcodeados
- Módulos dinámicos vs listas hardcodeadas
- Cache centralizado vs sistemas fragmentados

## Estructura de Directorios

```
src/proyector/                    # Sistema Proyector completo
├── index.js                      # Punto de entrada principal
├── store/                        # Store unificado Zustand
│   ├── index.js                  # Store principal con devtools
│   ├── slices/                   # Slices especializados
│   │   ├── authSlice.js          # Autenticación (mock)
│   │   ├── instanceSlice.js      # Instancias estáticas
│   │   ├── moduleSlice.js        # Módulos dinámicos
│   │   ├── entitySlice.js        # Entidades (mock)
│   │   ├── cacheSlice.js         # Cache unificado
│   │   └── uiSlice.js            # Estado de interfaz
│   └── middleware/               # Middleware personalizado
├── modules/                      # Sistema de módulos dinámicos
│   ├── index.js                  # ProyectorModuleSystem
│   ├── registry/                 # Registros centralizados
│   ├── loaders/                  # Cargadores de datos
│   └── types/                    # Tipos y validación
├── pageBlocks/                   # Sistema PageBlocks
│   ├── registry.js               # Registro de blocks
│   ├── PageBlockRenderer.js      # Renderizador principal
│   ├── controllers/              # Controladores de negocio
│   ├── templates/                # Templates React puros
│   └── utils/                    # Utilidades (permisos, config)
├── components/                   # Componentes UI
│   ├── layouts/                  # Layouts principales
│   ├── common/                   # Componentes comunes
│   └── pages/                    # Páginas (obsoletas con PageBlocks)
└── routes/                       # Sistema de routing
    └── index.js                  # Router principal
```

## Datos y Configuración

```
public/modules/                   # Módulos dinámicos (nuevo)
├── index.json                    # Índice de módulos y bundles
├── sales/                        # Módulo de ventas
│   ├── module.json               # Metadatos y permisos
│   └── pages.json                # Páginas públicas y panel
├── contacts/                     # Módulo de contactos
├── panel/                        # Módulo del panel
└── user/                         # Módulo de usuario

public/staticInstances/             # Instancias estáticas (nuevo)
├── demos/                        # Instancias de demostración
│   ├── restaurant/               # Demo restaurante
│   ├── retail/                   # Demo retail
│   └── services/                 # Demo servicios
└── clients/                      # Instancias de clientes
    ├── client1/                  # Cliente ejemplo 1
    └── client2/                  # Cliente ejemplo 2
```

## Flujo de Navegación

### Portada Principal (`/`)
- Lista de sites estáticos disponibles
- Enlaces directos a cada site
- Información básica de cada demo/cliente

### Sites Específicos (`/:siteSlug/`)
- Portada específica del site
- Branding personalizado por site
- Navegación a módulos disponibles

### Panel de Site (`/:siteSlug/panel`)
- Dashboard con módulos instalados
- Accesos rápidos a funcionalidades
- Estadísticas mock del site

### Módulos y Entidades (`/:siteSlug/entity/:entitySlug/:action`)
- Listados, formularios y vistas de entidades
- Renderizado via PageBlocks
- Datos mock para demostración

## Tecnologías y Patrones

### Store Unificado (Zustand)
- **Single source of truth** para todo el estado
- **Slices especializados** por dominio
- **DevTools integrado** para debugging
- **Subscripciones optimizadas** con selectores

### Sistema PageBlocks
- **Páginas declarativas** definidas como arrays de objetos
- **Separación Template/Controller/Config**
- **Permisos granulares** a nivel de block
- **Configuración dinámica** con interpolación

### Módulos Dinámicos
- **Carga desde JSON** vs hardcoding
- **Resolución de dependencias** automática
- **Gestión de bundles** centralizada
- **Compatibilidad backend** total

### Instancias Estáticas
- **Configuración por instancia** en JSON
- **Branding personalizado** (colores, fuentes, logos)
- **Módulos específicos** por tipo de negocio
- **Datos de demostración** coherentes

## Estado de Implementación

### ✅ Completado
- Store unificado con 6 slices especializados
- Sistema PageBlocks con 5 blocks básicos
- Módulos dinámicos con carga desde `/public/modules/`
- Router principal con rutas `/new/`
- Componentes UI básicos (layouts, menús, headers)

### 🟡 En Desarrollo
- Instancias estáticas con branding
- Integración de datos reales vs mocks
- Sistema de permisos granular
- Cache inteligente y optimizaciones

### 🔴 Pendiente
- Portada principal con lista de instancias
- Configuración de branding por instancia
- Datos de demostración coherentes
- Testing y validación completa

## Próximos Pasos

1. **Implementar instancias estáticas** con configuración JSON
2. **Crear portada principal** con lista de instancias
3. **Configurar branding** por instancia (colores, logos, fuentes)
4. **Generar datos mock** coherentes por tipo de negocio
5. **Optimizar navegación** entre instancias y módulos
6. **Documentar patrones** de desarrollo y uso
