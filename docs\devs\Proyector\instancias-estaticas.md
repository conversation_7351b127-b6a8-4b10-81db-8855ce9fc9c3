# Sistema de Instancias Estáticas

## Concepto y Objetivos

Las **Instancias Estáticas** son configuraciones predefinidas que permiten demostrar diferentes tipos de negocios y casos de uso del sistema Harmony, enfocándose exclusivamente en la **robustez de la UI** sin persistencia de datos ni autenticación real.

### Objetivos Principales
1. **Demostración de UI**: Mostrar cómo se ve el sistema en diferentes contextos
2. **Testing de Módulos**: Validar renderizado de módulos en diferentes configuraciones
3. **Desarrollo de Branding**: Probar diferentes esquemas de colores y diseños
4. **Navegación Fluida**: Experiencia de usuario sin recargas entre instancias

## Estructura de Instancias Estáticas

### Ubicación y Organización
```
public/staticInstances/
├── index.json                   # Índice de todas las instancias
├── demos/                       # Instancias de demostración
│   ├── restaurant/
│   │   ├── instance.json        # Configuración de la instancia
│   │   ├── branding.json        # Colores, fuentes, logos
│   │   ├── modules.json         # Módulos instalados
│   │   └── data/                # Datos mock por entidad
│   │       ├── contacts.json
│   │       ├── invoices.json
│   │       └── products.json
│   ├── retail/                  # Demo tienda retail
│   ├── services/                # Demo empresa de servicios
│   └── manufacturing/           # Demo empresa manufacturera
└── clients/                     # Instancias de clientes reales
    ├── client1/                 # Cliente ejemplo 1
    └── client2/                 # Cliente ejemplo 2
```

### Archivo Índice Principal (`public/staticInstances/index.json`)
```json
{
  "version": "1.0.0",
  "lastUpdated": "2024-01-15T10:00:00Z",
  "description": "Índice de instancias estáticas del Proyector",
  "instances": [
    {
      "hash": "restaurant-demo",
      "name": "Restaurante La Cocina",
      "type": "demo",
      "category": "restaurant",
      "description": "Demo de restaurante con gestión de mesas, pedidos y facturación",
      "bundles": ["base", "pos", "restaurant"],
      "status": "active",
      "featured": true,
      "preview": "/images/previews/restaurant-demo.jpg"
    },
    {
      "hash": "retail-demo", 
      "name": "Tienda Fashion Store",
      "type": "demo",
      "category": "retail",
      "description": "Demo de tienda retail con inventario, ventas y clientes",
      "bundles": ["base", "pos", "inventory"],
      "status": "active",
      "featured": true,
      "preview": "/images/previews/retail-demo.jpg"
    },
    {
      "hash": "services-demo",
      "name": "Consultora TechSolutions", 
      "type": "demo",
      "category": "services",
      "description": "Demo de empresa de servicios con proyectos y facturación",
      "bundles": ["base", "projects", "time-tracking"],
      "status": "active",
      "featured": false,
      "preview": "/images/previews/services-demo.jpg"
    }
  ],
  "categories": {
    "restaurant": {
      "name": "Restaurantes",
      "description": "Gestión de restaurantes, bares y cafeterías",
      "icon": "restaurant-outline",
      "color": "#FF6B35"
    },
    "retail": {
      "name": "Retail",
      "description": "Tiendas, comercios y puntos de venta",
      "icon": "storefront-outline", 
      "color": "#4ECDC4"
    },
    "services": {
      "name": "Servicios",
      "description": "Empresas de servicios profesionales",
      "icon": "briefcase-outline",
      "color": "#45B7D1"
    }
  }
}
```

## Configuración de Instancia

### Archivo de Instancia (`instance.json`)
```json
{
  "hash": "restaurant-demo",
  "name": "Restaurante La Cocina",
  "type": "demo",
  "category": "restaurant",
  "description": "Restaurante familiar especializado en comida mediterránea",
  "
  "contact": {
    "address": "Calle Principal 123, Ciudad",
    "phone": "+**************",
    "email": "<EMAIL>",
    "website": "https://lacocina.com"
  },
  
  "business": {
    "industry": "Restauración",
    "size": "small",
    "employees": 15,
    "founded": "2018",
    "currency": "EUR",
    "timezone": "Europe/Madrid",
    "language": "es"
  },
  
  "modules": {
    "installed": ["panel", "contacts", "sales", "inventory", "tables"],
    "bundles": ["base", "pos", "restaurant"],
    "mainModules": ["tables", "sales", "inventory", "contacts"]
  },
  
  "settings": {
    "showWelcomeMessage": true,
    "enableNotifications": false,
    "defaultView": "dashboard",
    "enableTutorials": true
  }
}
```

### Sistema de Branding (`branding.json`)

#### Configuración Básica
```json
{
  "version": "1.0.0",
  "instanceHash": "restaurant-demo",
  "
  "basic": {
    "businessName": "Restaurante La Cocina",
    "tagline": "Sabores auténticos del Mediterráneo",
    "description": "Restaurante familiar con más de 5 años ofreciendo la mejor comida mediterránea de la ciudad"
  },
  
  "images": {
    "profileImage": "/images/instances/restaurant-demo/profile.jpg",
    "logoLight": "/images/instances/restaurant-demo/logo-light.svg",
    "logoDark": "/images/instances/restaurant-demo/logo-dark.svg", 
    "favicon": "/images/instances/restaurant-demo/favicon.ico",
    "banner": "/images/instances/restaurant-demo/banner.jpg"
  }
}
```

#### Sistema de Colores Corporativos
```json
{
  "colors": {
    "primary": {
      "base": "#FF6B35",
      "contrast": "#FFFFFF",
      "tints": ["#FF8A5C", "#FFA983", "#FFC8AA"],
      "shades": ["#E55A2B", "#CC4A21", "#B33A17"],
      "description": "Naranja cálido mediterráneo"
    },
    "secondary": {
      "base": "#2E8B57", 
      "contrast": "#FFFFFF",
      "tints": ["#4FA070", "#70B589", "#91CAA2"],
      "shades": ["#267A4A", "#1E693D", "#165830"],
      "description": "Verde oliva natural"
    },
    "accent": {
      "base": "#F4A261",
      "contrast": "#2A2A2A", 
      "tints": ["#F6B482", "#F8C6A3", "#FAD8C4"],
      "shades": "#DC9250", "#C4823F", "#AC722E"],
      "description": "Dorado cálido"
    },
    "neutral": {
      "base": "#6C757D",
      "contrast": "#FFFFFF",
      "tints": ["#868E96", "#A0A7AF", "#BAC0C8"],
      "shades": ["#5A6268", "#484F54", "#363C40"],
      "description": "Gris equilibrado"
    },
    "success": {
      "base": "#28A745",
      "contrast": "#FFFFFF", 
      "description": "Verde éxito"
    },
    "warning": {
      "base": "#FFC107",
      "contrast": "#2A2A2A",
      "description": "Amarillo advertencia"
    },
    "danger": {
      "base": "#DC3545", 
      "contrast": "#FFFFFF",
      "description": "Rojo peligro"
    }
  }
}
```

#### Sistema de Tipografía
```json
{
  "fonts": {
    "primary": {
      "name": "Inter",
      "fallback": "system-ui, -apple-system, sans-serif",
      "weights": [400, 500, 600, 700],
      "usage": "Textos principales, navegación, botones",
      "googleFonts": "Inter:wght@400;500;600;700"
    },
    "secondary": {
      "name": "Playfair Display", 
      "fallback": "Georgia, serif",
      "weights": [400, 600, 700],
      "usage": "Títulos, encabezados, elementos decorativos",
      "googleFonts": "Playfair+Display:wght@400;600;700"
    }
  }
}
```

## Datos Mock por Instancia

### Estructura de Datos Mock
```
public/staticInstances/restaurant-demo/data/
├── contacts.json              # Clientes y proveedores
├── invoices.json              # Facturas de ejemplo
├── products.json              # Productos del menú
├── tables.json                # Mesas del restaurante
├── orders.json                # Pedidos de ejemplo
└── inventory.json             # Inventario de ingredientes
```

### Ejemplo de Datos Mock (`contacts.json`)
```json
{
  "version": "1.0.0",
  "instanceHash": "restaurant-demo",
  "entitySlug": "contacts",
  "description": "Clientes y proveedores del restaurante",
  "data": [
    {
      "id": "contact-001",
      "type": "customer",
      "name": "María García López",
      "email": "<EMAIL>",
      "phone": "+34 666 123 456",
      "address": "Calle Mayor 45, Madrid",
      "customerSince": "2023-03-15",
      "totalOrders": 23,
      "totalSpent": 1250.50,
      "favoriteTable": "Mesa 7",
      "preferences": ["Sin gluten", "Vegetariano"],
      "lastVisit": "2024-01-10"
    },
    {
      "id": "contact-002", 
      "type": "supplier",
      "name": "Distribuidora Mediterránea S.L.",
      "email": "<EMAIL>",
      "phone": "+34 912 345 678",
      "address": "Polígono Industrial Norte, Nave 15",
      "supplierSince": "2022-01-20",
      "category": "Productos frescos",
      "paymentTerms": "30 días",
      "lastDelivery": "2024-01-12"
    }
  ]
}
```

## Navegación y Rutas

### Portada Principal (`/`)
- **Lista de sites** con previews y descripciones
- **Filtrado por categoría** (restaurant, retail, services)
- **Sites destacados** (featured)
- **Acceso directo** a cada site

### Portada de Site (`/:siteSlug/`)
- **Branding personalizado** del site
- **Información del negocio** (descripción, contacto)
- **Módulos disponibles** con accesos directos
- **Botón de acceso al panel** interno

### Panel de Site (`/:siteSlug/panel`)
- **Dashboard personalizado** con branding
- **Estadísticas mock** relevantes al tipo de negocio
- **Accesos rápidos** a módulos principales
- **Navegación** a listados y formularios

## Implementación Técnica

### Store de Instancias (`instanceSlice.js`)
```javascript
const instanceSlice = (set, get) => ({
  instances: {
    available: [],        // Lista de instancias disponibles
    current: null,        // Instancia actualmente seleccionada
    branding: {},         // Configuración de branding actual
    data: {},             // Datos mock de la instancia
    loading: false,       // Estado de carga
    error: null           // Errores de carga
  },
  
  instanceActions: {
    loadInstances: async () => {
      // Cargar índice de instancias desde public/staticInstances/
    },
    selectInstance: async (instanceHash) => {
      // Cargar configuración específica de la instancia
    },
    loadInstanceData: async (instanceHash, entitySlug) => {
      // Cargar datos mock para una entidad específica
    }
  }
});
```

### Componente de Portada Principal
```javascript
// src/proyector/components/pages/InstancesHomePage.js
export function InstancesHomePage() {
  const { available, loading } = useInstances();
  const { loadInstances } = useInstanceActions();
  
  useEffect(() => {
    loadInstances();
  }, []);
  
  return (
    <div className="instances-home">
      <header className="instances-header">
        <h1>Proyector Harmony</h1>
        <p>Explora diferentes tipos de negocios</p>
      </header>
      
      <div className="instances-grid">
        {available.map(instance => (
          <InstanceCard 
            key={instance.hash}
            instance={instance}
            onClick={() => navigateToInstance(instance.hash)}
          />
        ))}
      </div>
    </div>
  );
}
```

## Arquitectura Final Implementada

### **Estado de Implementación: ✅ COMPLETADO**

El sistema de instancias estáticas ha sido completamente implementado siguiendo la arquitectura PageBlocks del Proyector.

### **Estructura Final de Rutas**
```
/new                           → instances/instances-home (Portada)
/new/:instance                 → instances/instance-info (Info específica)
/new/a/panel                   → panel/panel (Panel simplificado)
/new/:instance/a/panel         → panel/panel (Panel con branding)
```

### **Módulos Implementados**

#### **Módulo instances**
```json
{
  "slug": "instances",
  "scope": "global",
  "pages": {
    "public": {
      "instances-home": "Portada principal",
      "instance-info": "Información específica"
    }
  }
}
```

**PageBlocks del módulo instances:**
- **InstancesHeroBlock**: Hero section con branding dinámico y logos
- **InstancesListBlock**: Lista con cards que aplican colores de branding
- **InstanceInfoBlock**: Información detallada con branding completo
- **InstancesQuickAccessBlock**: Acceso rápido con colores dinámicos

#### **Módulo panel (Simplificado)**
```json
{
  "slug": "panel",
  "scope": "global",
  "pages": {
    "panel": {
      "panel": "Panel principal simplificado"
    }
  }
}
```

**PageBlocks del panel:**
- **InstancesQuickAccessBlock**: Solo acceso rápido a instancias

### **Instancias Configuradas**

#### **1. Instancia Main (Sistema)**
- **Hash**: `main`
- **Categoría**: `system`
- **Branding**: Colores por defecto
- **Propósito**: Instancia base del sistema

#### **2. Restaurante La Bella Vista**
- **Hash**: `restaurant`
- **Categoría**: `restaurant`
- **Branding**: Colores cálidos (naranja/verde)
- **Stats**: Mesas, menú, pedidos, ocupación

#### **3. Fashion Store**
- **Hash**: `retail`
- **Categoría**: `retail`
- **Branding**: Colores vibrantes (púrpura/rosa)
- **Stats**: Productos, categorías, ventas, stock

#### **4. TechSolutions Consultora**
- **Hash**: `services`
- **Categoría**: `services`
- **Branding**: Colores profesionales (verde/azul)
- **Stats**: Proyectos, clientes, equipo, éxito

### **Sistema de Branding Simplificado**

#### **Formato Corregido (1 tint, 1 shade por color)**
```json
{
  "colors": {
    "primary": {
      "base": "#d97706",
      "contrast": "#ffffff",
      "tint": "#f59e0b",      // Un solo tint
      "shade": "#b45309"      // Un solo shade
    }
  }
}
```

#### **Aplicación Dinámica**
- CSS variables automáticas por instancia
- Compatibilidad con Ionic
- Transiciones suaves entre instancias

### **Flujo de Usuario Implementado**

#### **1. Entrada al Sistema**
```
Usuario → /new → Portada de instancias
```
- Hero section explicativo
- Lista de instancias con filtros
- Instancias destacadas

#### **2. Exploración de Instancia**
```
Portada → /new/:instance → Información específica
```
- Branding aplicado automáticamente
- Información del negocio
- Estadísticas clave
- Módulos disponibles

#### **3. Acceso al Panel**
```
Información → /new/:instance/a/panel → Panel con branding
```
- Branding de instancia aplicado
- Acceso rápido a otras instancias
- Navegación fluida

#### **4. Panel Principal**
```
Sistema → /new/a/panel → Panel simplificado
```
- Lista de instancias disponibles
- Botones de acceso directo
- Vista de información rápida

### **Componentes Técnicos Implementados**

#### **PageBlockRenderer Mejorado**
- Soporte para rutas `module/page`
- Parsing automático de módulo y página
- Compatibilidad con formato legacy
- Búsqueda en módulo específico

#### **Store de Instancias**
- Carga lazy de configuraciones
- Cache de branding aplicado
- Estado persistente entre navegaciones
- Aplicación automática de CSS

#### **Templates Optimizados**
- Sin dependencias CSS externas
- Estilos inline e Ionic únicamente
- Responsive design completo
- Accesibilidad básica

### **Archivos Implementados**

#### **Configuración de Instancias**
```
public/staticInstances/
├── index.json                           # Índice principal
├── demos/restaurant/
│   ├── instance.json                    # Config del restaurante
│   └── branding.json                    # Branding mediterráneo
├── demos/retail/
│   ├── instance.json                    # Config de tienda
│   └── branding.json                    # Branding moderno
└── demos/services/
    ├── instance.json                    # Config consultora
    └── branding.json                    # Branding profesional
```

#### **Sistema de Branding Mejorado**
```
src/proyector/
├── utils/brandingUtils.js               # Utilidades de branding mejoradas
├── hooks/useBranding.js                 # Hook para acceso al branding
└── store/slices/instanceSlice.js        # Store con branding integrado
```

#### **Módulos y PageBlocks**
```
public/modules/instances/
├── module.json                          # Definición del módulo
└── pages.json                           # Páginas con PageBlocks

src/proyector/pageBlocks/
├── templates/
│   ├── InstancesHeroTemplate.js         # Hero section
│   ├── InstancesListTemplate.js         # Lista con filtros
│   ├── InstanceInfoTemplate.js          # Info específica
│   └── InstancesQuickAccessTemplate.js  # Acceso rápido
├── controllers/
│   ├── instancesHeroController.js
│   ├── instancesListController.js
│   ├── instanceInfoController.js
│   └── instancesQuickAccessController.js
└── registry.js                          # Blocks registrados
```

### **Beneficios de la Arquitectura**

#### **1. Simplicidad**
- Panel enfocado en navegación de instancias
- Flujo de usuario claro e intuitivo
- Configuración declarativa

#### **2. Escalabilidad**
- Fácil agregar nuevas instancias
- Sistema de branding extensible
- PageBlocks reutilizables

#### **3. Mantenibilidad**
- Separación clara de responsabilidades
- Código limpio y organizado
- Documentación completa

#### **4. Performance**
- Carga lazy de configuraciones
- Cache inteligente de branding
- Optimización de rendering

### **Casos de Uso**

#### **Demo de Ventas**
1. Cliente entra a `/new`
2. Ve diferentes tipos de negocios
3. Selecciona "Restaurante" → `/new/restaurant`
4. Explora información con branding mediterráneo
5. Accede al panel → `/new/restaurant/a/panel`
6. Navega con branding aplicado

#### **Desarrollo y Testing**
1. Desarrollador accede a `/new/a/panel`
2. Ve lista de instancias disponibles
3. Prueba diferentes configuraciones
4. Verifica branding y funcionalidades

#### **Presentación Comercial**
1. Presentación comienza en `/new`
2. Muestra versatilidad del sistema
3. Demuestra diferentes sectores
4. Navegación fluida entre demos

### **Sistema de Branding Dinámico Implementado**

#### **Características del Branding**

##### **1. Aplicación Automática de Colores**
- **CSS Variables**: Colores aplicados como `--ion-color-primary`, `--color-primary`, etc.
- **Compatibilidad Ionic**: Variables específicas para componentes Ionic
- **Tints y Shades**: Variaciones automáticas de colores para estados hover/active

##### **2. Fuentes Dinámicas**
- **Google Fonts**: Carga automática desde CDN
- **CSS Variables**: `--font-primary`, `--font-secondary`, `--font-heading`, `--font-body`
- **Fallbacks**: Fuentes de respaldo para mejor compatibilidad

##### **3. Logos e Imágenes**
- **Logos Dinámicos**: Soporte para versiones light/dark
- **Banners**: Imágenes de fondo en hero sections
- **Favicon**: Actualización automática del favicon
- **CSS Variables**: Acceso a imágenes via `--logo-light`, `--banner-image`

##### **4. Hooks de Branding**
```javascript
// Hook principal
const { branding, hasLogo, isLoaded } = useBranding();

// Hook de colores
const { getColor, getCSSVar, primary } = useBrandingColors();

// Hook de fuentes
const { getFontFamily, getFontCSS } = useBrandingFonts();

// Hook de imágenes
const { logoLight, banner, getLogo } = useBrandingImages();

// Hook de estilos
const { getHeroStyle, getCardStyle, getButtonStyle } = useBrandingStyles();
```

##### **5. Templates Actualizados**
- **InstancesHeroTemplate**: Logos, gradientes y fuentes dinámicas
- **InstanceInfoTemplate**: Branding completo en hero section
- **InstancesListTemplate**: Colores de categoría desde branding
- **InstancesQuickAccessTemplate**: Estilos dinámicos en títulos y cards

#### **Configuración de Branding por Instancia**

##### **Restaurante (Mediterráneo)**
- **Colores**: Naranja cálido (#d97706) + Verde oliva (#2e8b57)
- **Fuentes**: Playfair Display + Inter
- **Estilo**: Cálido, familiar, mediterráneo

##### **Retail (Moderno)**
- **Colores**: Púrpura vibrante (#7c3aed) + Rosa (#ec4899)
- **Fuentes**: Poppins + Inter
- **Estilo**: Moderno, juvenil, dinámico

##### **Servicios (Profesional)**
- **Colores**: Verde tecnológico (#059669) + Azul (#0ea5e9)
- **Fuentes**: Space Grotesk + Inter
- **Estilo**: Profesional, confiable, técnico

#### **Flujo de Aplicación de Branding**

1. **Carga de Instancia**: `instanceActions.selectInstance(hash)`
2. **Carga de Branding**: `instanceActions.loadInstanceBranding(hash)`
3. **Aplicación Automática**: `brandingUtils.applyBranding(branding)`
4. **CSS Variables**: Disponibles inmediatamente en toda la aplicación
5. **Hooks**: Acceso reactivo al branding en componentes

---

**Estado**: ✅ Implementación Completa con Branding Dinámico
**Versión**: 2.2.0
**Arquitectura**: PageBlocks + Branding Dinámico + Panel Simplificado
**Última actualización**: Enero 2024
