/**
 * System Initializer - Detección de HyperContext vacío y creación automática del main site
 * 
 * Implementa la lógica de inicialización del sistema según big-picture architecture:
 * - Detecta cuando el HyperContext está vacío (primera vez)
 * - Crea automáticamente el "main site" 
 * - Asigna el primer usuario registrado como super admin
 * 
 * ARQUITECTURA: Según docs/Proyector/v1.2/big-picture/sites-architecture.md
 */

import { HyperContextEnhanced } from './HyperContextEnhanced.js';
import SiteManager from './sites/SiteManager.js';
import { UserManager } from './UserManager.js';
import { DEFAULT_PROYECTOR_ROLES } from './config/defaultRoles.js';
import { PermissionController } from './security/PermissionController.js';
import { RoleManager } from './security/RoleManager.js';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import admin from 'firebase-admin';

// ✅ CORRECCIÓN: Cache global para evitar re-inicializaciones
const SYSTEM_INITIALIZATION_CACHE = {
  initialized: false,
  timestamp: null,
  ttl: 5 * 60 * 1000, // 5 minutos
  isInitializing: false
};

export class SystemInitializer {
  constructor(config = {}) {
    this.db = getFirestore();
    this.storage = getStorage(); // ✅ CORRECCIÓN: Inicializar storage
    this.admin = admin; // ✅ CORRECCIÓN: Agregar admin para acceso a Auth
    this.config = {
      mainSiteId: 'main',
      mainSiteName: 'Tranqui Pro',
      mainSiteDescription: 'Sistema administrativo principal del Proyector',
      enableAutoInitialization: true,
      ...config
    };

    // ✅ CORRECCIÓN: Usar el mismo prefijo que HyperContextEnhanced
    const isDevelopment = process.env.PROYECTOR_FUNCTIONS_EMULATOR === 'true' ||
                         process.env.NODE_ENV === 'development' ||
                         process.env.FUNCTIONS_EMULATOR === 'true';

    const hyperContextPrefix = (process.env.PROYECTOR_HYPERCONTEXT_PREFIX ||
                               (isDevelopment ? 'HyperContext_dev' : 'HyperContext')).trim();

    // ✅ CORRECCIÓN: Pasar storage a HyperContextEnhanced
    this.hyperContext = new HyperContextEnhanced({
      db: this.db,
      prefix: hyperContextPrefix,
      config: {
        siteId: this.config.mainSiteId,
        storage: this.storage  // ✅ CORRECCIÓN: Pasar storage en config
      }
    });
    
    // Para la inicialización del sistema, no necesitamos SiteManager completo
    // Solo necesitamos acceso directo a HyperContext
    this.siteManager = null; // Se inicializará después si es necesario
    this.userManager = new UserManager({
      hyperContext: this.hyperContext,
      config
    });

    // ✅ PROYECTOR: Inicializar PermissionController y RoleManager
    this.permissionController = new PermissionController(this.hyperContext);
    this.roleManager = new RoleManager(this.hyperContext);

    // ✅ CORRECCIÓN: Usar cache global para evitar re-inicializaciones
    this.initialized = SYSTEM_INITIALIZATION_CACHE.initialized;
    this.isInitializing = SYSTEM_INITIALIZATION_CACHE.isInitializing;
  }

  /**
   * Inicializar el sistema y detectar si es la primera vez
   */
  async initialize() {
    // ✅ CORRECCIÓN: Verificar cache global primero
    const now = Date.now();
    if (SYSTEM_INITIALIZATION_CACHE.initialized &&
        SYSTEM_INITIALIZATION_CACHE.timestamp &&
        (now - SYSTEM_INITIALIZATION_CACHE.timestamp) < SYSTEM_INITIALIZATION_CACHE.ttl) {
      console.log('🔄 SystemInitializer: Usando estado desde cache global');
      return;
    }

    if (this.initialized || this.isInitializing || SYSTEM_INITIALIZATION_CACHE.isInitializing) {
      console.log('🔄 SystemInitializer: Ya está inicializado o en proceso');
      return;
    }

    try {
      // ✅ CORRECCIÓN: Actualizar cache global
      this.isInitializing = true;
      SYSTEM_INITIALIZATION_CACHE.isInitializing = true;

      console.log('🔍 SystemInitializer: Verificando estado del sistema...');

      // ✅ CORRECCIÓN CRÍTICA: Inicializar componentes base con opciones de systemInitialization
      await this.hyperContext.initialize({ systemInitialization: true });
      await this.userManager.initialize();

      // Verificar si el sistema está vacío
      const isEmpty = await this.detectEmptyHyperContext();

      if (isEmpty && this.config.enableAutoInitialization) {
        console.log('🚀 SystemInitializer: Sistema vacío detectado, iniciando configuración inicial...');
        await this.performInitialSetup();
      } else {
        console.log('✅ SystemInitializer: Sistema ya inicializado');
      }

      // ✅ CORRECCIÓN: Actualizar cache global
      this.initialized = true;
      this.isInitializing = false;
      SYSTEM_INITIALIZATION_CACHE.initialized = true;
      SYSTEM_INITIALIZATION_CACHE.isInitializing = false;
      SYSTEM_INITIALIZATION_CACHE.timestamp = now;

    } catch (error) {
      // ✅ CORRECCIÓN: Limpiar cache global en caso de error
      this.isInitializing = false;
      SYSTEM_INITIALIZATION_CACHE.isInitializing = false;
      console.error('❌ SystemInitializer: Error durante inicialización:', error);
      throw error;
    }
  }

  /**
   * Detectar si el HyperContext está vacío (primera vez)
   */
  async detectEmptyHyperContext() {
    try {
      // ✅ CORRECCIÓN: Verificar si existe el main site usando accessGlobalEntity
      const mainSite = await this.hyperContext.accessGlobalEntity('sites', this.config.mainSiteId, {
        systemInitialization: true
      });
      if (mainSite) {
        return false; // Sistema ya inicializado
      }
      
      // ✅ CORRECCIÓN: Verificar sites usando HyperContext con flag de inicialización
      try {
        const sitesData = await this.hyperContext.accessGlobalEntity('sites', '_init', {
          systemInitialization: true
        });
        if (sitesData) {
          return false; // Hay sites existentes
        }
      } catch (error) {
        // Si no existe, continuar verificando
      }

      // ✅ CORRECCIÓN: Verificar usuarios usando path directo de HyperContext
      try {
        const usersData = await this.hyperContext.get('users[_init]', {
          systemInitialization: true
        });
        if (usersData) {
          // Hay usuarios pero no sites - situación de migración
          console.log('⚠️ SystemInitializer: Usuarios existentes sin main site, creando main site...');
          return true;
        }
      } catch (error) {
        // Si no existe, continuar
      }
      
      console.log('🔍 SystemInitializer: HyperContext completamente vacío detectado');
      return true;
      
    } catch (error) {
      console.error('❌ Error detectando estado del HyperContext:', error);
      // En caso de error, asumir que no está vacío para evitar duplicaciones
      return false;
    }
  }

  /**
   * Realizar configuración inicial del sistema
   */
  async performInitialSetup() {
    try {
      console.log('🏗️ SystemInitializer: Iniciando configuración inicial del sistema...');
      
      // 1. Crear el main site
      await this.createMainSite();
      
      // 2. Configurar estructura base de datos
      await this.setupBaseDataStructure();

      // 2.1. Migrar rutas legacy a arquitectura site-specific
      await this.migrateLegacyRoutes();

      // 3. Crear configuraciones del site principal
      await this.createMainSiteSettings();
      
      console.log('✅ SystemInitializer: Configuración inicial completada');
      
    } catch (error) {
      console.error('❌ Error durante configuración inicial:', error);
      throw error;
    }
  }

  /**
   * Crear el main site administrativo
   */
  async createMainSite() {
    try {
      console.log('🏢 SystemInitializer: Creando main site...');

      const mainSiteData = {
        id: this.config.mainSiteId,
        name: this.config.mainSiteName,
        description: this.config.mainSiteDescription,
        type: 'admin-interface',

        // Configuración específica del main site
        settings: {
          status: 'active',
          visibility: 'private',
          isMainSite: true,
          adminInterface: true,
          seo: {},
          analytics: {}
        },

        // Módulos habilitados
        modules: [
          { id: 'sites', name: 'Sites Management', enabled: true },
          { id: 'codeblocks', name: 'CodeBlocks', enabled: true },
          { id: 'users', name: 'User Management', enabled: true },
          { id: 'analytics', name: 'Analytics', enabled: true },
          { id: 'settings', name: 'Settings', enabled: true }
        ],

        // ✅ CORRECCIÓN: pages debe ser subcolección, no objeto en documento principal

        // Metadatos
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'system',
          updatedBy: 'system',
          version: '1.0.0',
          status: 'active',
          isSystemGenerated: true
        },

        // ✅ CORRECCIÓN: Ownership completo y robusto - SIN ARRAYS
        ownership: {
          owner: null, // Se asignará al primer usuario
          permissions: {},
          createdAt: new Date().toISOString(),
          accessLevel: 'private', // private, public, restricted
          // NOTA: collaborators, transferHistory, invitations son SUBCOLLECTIONS REALES
          // Paths: sites[main].ownershipCollaborators[], sites[main].ownershipTransferHistory[], sites[main].ownershipInvitations[]
        },

        // ✅ CORRECCIÓN: Sistema de roles del site
        roles: {
          admin: {
            name: 'Administrador',
            permissions: [
              'site.manage',
              'users.manage',
              'content.create',
              'content.edit',
              'content.delete',
              'content.publish',
              'modules.manage',
              'settings.manage',
              'billing.manage'
            ],
            isSystemRole: true
          },
          editor: {
            name: 'Editor',
            permissions: [
              'content.create',
              'content.edit',
              'content.publish',
              'modules.use'
            ],
            isSystemRole: true
          },
          viewer: {
            name: 'Visualizador',
            permissions: [
              'content.view',
              'modules.view'
            ],
            isSystemRole: true
          },
          custom: {}
        },

        // ✅ CORRECCIÓN: Bundles y límites integrados
        bundles: {
          current: 'essential',
          history: [
            {
              plan: 'essential',
              activatedAt: new Date().toISOString(),
              activatedBy: 'system',
              status: 'active'
            }
          ],
          limits: {
            maxUsers: 3,
            maxStorage: 1000,
            maxInvoices: 50,
            maxContacts: 100,
            maxProducts: 50,
            docsDisponiblesPorMes: 100,
            docsDisponiblesTotal: 500,
            tokensLLMConsumiblesPorMes: 10000,
            paquetesExtrasTokens: {
              disponible: false,
              precio: 0,
              vencimiento: "6 meses",
              tokensIncluidos: 0
            }
          },
          usage: {
            users: 0,
            storage: 0,
            invoices: 0,
            contacts: 0,
            products: 0,
            docsThisMonth: 0,
            docsTotal: 0,
            tokensThisMonth: 0,
            lastUpdated: new Date().toISOString()
          },
          extras: []
        },

        // ✅ CORRECCIÓN: Datos de billing - SIN ARRAYS
        billing: {
          plan: 'essential',
          status: 'active',
          nextBilling: null,
          paymentMethod: null,
          credits: 0,
          currency: 'USD',
          taxInfo: null,
          billingAddress: null
          // NOTA: billingHistory, invoices son SUBCOLLECTIONS REALES
          // Paths: sites[main].billingHistory[], sites[main].billingInvoices[]
        },

        // Performance y cache
        performance: {
          cacheEnabled: true,
          compressionEnabled: true,
          cdnEnabled: false
        },

        // Configuración de branding
        branding: {
          name: this.config.mainSiteName,
          logo: '/assets/logos/tranqui-pro-logo.svg',
          favicon: '/assets/icons/tranqui-pro-favicon.ico',
          colors: {
            primary: '#2dd4bf',
            secondary: '#64748b',
            tertiary: '#f1f5f9'
          },
          theme: 'admin-dark'
        }
      };

      // ✅ CORRECCIÓN: Usar path directo de HyperContext para sites
      await this.hyperContext.set(`sites[${this.config.mainSiteId}]`, mainSiteData, {
        systemInitialization: true
      });

      // ✅ CORRECCIÓN: Crear estructura de cache en storage automáticamente
      await this._createSiteStorageStructure(this.config.mainSiteId);

      // ✅ CORRECCIÓN: Crear páginas por defecto como subcolección
      await this._createDefaultPages(this.config.mainSiteId);

      // ✅ NUEVO: Inicializar subcollections que antes eran arrays
      await this._initializeMainSiteSubcollections(this.config.mainSiteId);

      // ✅ NUEVO: Cargar y cachear todos los datos del site
      await this._loadAndCacheInitialSiteData(this.config.mainSiteId);

      // ✅ ARQUITECTURA CORRECTA: Inicializar roles del sistema
      await this._initializeSystemRoles(this.config.mainSiteId);

      console.log('✅ Main site creado exitosamente:', this.config.mainSiteId);
      return mainSiteData;

    } catch (error) {
      console.error('❌ Error creando main site:', error);
      throw error;
    }
  }

  /**
   * Configurar estructura base de datos usando HyperContext
   */
  async setupBaseDataStructure() {
    try {
      console.log('📊 SystemInitializer: Configurando estructura base de datos en HyperContext...');

      // ✅ CORRECCIÓN: Solo crear entidades globales que realmente deben ser globales
      // settings y permissions deben ser site-specific, no globales
      const globalCollections = [
        'users',
        'logs'
      ];

      for (const collection of globalCollections) {
        const initData = {
          initialized: true,
          createdAt: new Date(),
          createdBy: 'SystemInitializer'
        };

        // ✅ CORRECCIÓN: Usar HyperContext con flag de inicialización del sistema
        await this.hyperContext.setGlobalEntity(collection, '_init', initData, {
          systemInitialization: true
        });
      }

      console.log('✅ Entidades globales inicializadas (users, logs)');

      console.log('✅ Estructura base de datos configurada en HyperContext');

    } catch (error) {
      console.error('❌ Error configurando estructura base:', error);
      throw error;
    }
  }

  /**
   * ✅ REFACTORIZADO: Crear configuraciones del site principal (NO globales)
   */
  async createMainSiteSettings() {
    try {
      console.log('⚙️ SystemInitializer: Creando configuraciones del site principal...');

      const mainSiteSettings = {
        system: {
          version: '1.2.0',
          initialized: true,
          initializedAt: new Date(),
          isMainSite: true,
          enableAutoUserPromotion: true, // Primer usuario se convierte en super admin
          enableMainSiteAccess: true
        },

        security: {
          requireEmailVerification: false, // Deshabilitado para primer usuario
          enableTwoFactor: false,
          sessionTimeout: 24 * 60 * 60 * 1000, // 24 horas
          maxLoginAttempts: 5
        },

        features: {
          enableSitesManagement: true,
          enableCodeBlocks: true,
          enableAI: true,
          enableAnalytics: true,
          enableBackup: true
        }
      };

      // ✅ REFACTORIZACIÓN: Guardar en site-specific settings
      await this.hyperContext.set(`sites[${this.config.mainSiteId}].settings[system]`, mainSiteSettings, {
        systemInitialization: true
      });
      console.log('✅ Configuraciones del site principal creadas');

    } catch (error) {
      console.error('❌ Error creando configuraciones del site principal:', error);
      throw error;
    }
  }

  /**
   * Asignar el primer usuario como super admin del main site
   */
  async assignFirstUserAsSuperAdmin(userId, firebaseUser = null) {
    try {
      console.log('👑 SystemInitializer: Asignando primer usuario como super admin...', userId);

      // ✅ PASO 1: Crear sesión completa del usuario usando la nueva función
      console.log('🔧 Creando sesión completa del usuario...');

      // Obtener información del usuario desde Firebase Auth o usar la proporcionada
      let userFirebaseData = firebaseUser;
      if (!userFirebaseData) {
        const userRecord = await this.admin.auth().getUser(userId);
        userFirebaseData = {
          uid: userRecord.uid,
          email: userRecord.email,
          displayName: userRecord.displayName,
          photoURL: userRecord.photoURL,
          emailVerified: userRecord.emailVerified
        };
      }

      // Llamar a la función de creación de sesión
      const sessionResult = await this.createUserSession({
        userId,
        siteId: this.config.mainSiteId,
        firebaseUser: userFirebaseData,
        isFirstUser: true
      });

      console.log('✅ Sesión de usuario creada:', sessionResult);

      // ✅ PASO 2: Verificar que el main site existe
      console.log('🔧 Verificando que el main site existe...');
      const mainSite = await this.hyperContext.get(`sites[${this.config.mainSiteId}]`);
      if (!mainSite) {
        throw new Error('Main site no encontrado');
      }

      // ✅ PASO 3: Actualizar ownership del main site
      console.log('🔧 Asignando ownership del main site...');
      mainSite.ownership.owner = userId;
      mainSite.ownership.permissions[userId] = ['*']; // Todos los permisos
      mainSite.metadata.updatedAt = new Date().toISOString();
      mainSite.metadata.firstUserAssigned = true;

      await this.hyperContext.set(`sites[${this.config.mainSiteId}]`, mainSite);
      console.log('✅ Ownership del main site asignado');

      // ✅ PASO 4: Crear registro de ownership en subcollection
      console.log('🔧 Creando registro de ownership...');
      const ownershipRecord = {
        userId,
        type: 'owner',
        permissions: ['*'],
        assignedAt: new Date().toISOString(),
        assignedBy: 'SystemInitializer',
        isFirstUser: true,
        status: 'active'
      };

      await this.hyperContext.set(`sites[${this.config.mainSiteId}].ownershipCollaborators[${userId}]`, ownershipRecord);
      console.log('✅ Registro de ownership creado');

      // ✅ PASO 5: Actualizar configuraciones del site principal
      console.log('🔧 Actualizando configuraciones del site principal...');
      const siteSettings = await this.hyperContext.get(`sites[${this.config.mainSiteId}].settings[system]`);
      if (siteSettings) {
        siteSettings.system.firstUserId = userId;
        siteSettings.system.firstUserAssignedAt = new Date().toISOString();
        await this.hyperContext.set(`sites[${this.config.mainSiteId}].settings[system]`, siteSettings);
        console.log('✅ Configuraciones del site principal actualizadas');
      }

      console.log('🎉 Primer usuario asignado como super admin exitosamente');
      return {
        success: true,
        userId,
        sessionResult,
        ownershipRecord,
        mainSiteId: this.config.mainSiteId
      };

    } catch (error) {
      console.error('❌ Error asignando primer usuario como super admin:', error);
      console.error('❌ Stack trace:', error.stack);
      throw error;
    }
  }

  /**
   * Verificar si el sistema necesita inicialización
   */
  async needsInitialization() {
    try {
      const isEmpty = await this.detectEmptyHyperContext();
      return isEmpty;
    } catch (error) {
      console.error('❌ Error verificando necesidad de inicialización:', error);
      return false;
    }
  }

  /**
   * ✅ REFACTORIZADO: Obtener estado del sistema desde site principal
   */
  async getSystemStatus() {
    try {
      // ✅ REFACTORIZACIÓN: Obtener configuraciones del site principal
      const siteSettings = await this.hyperContext.get(`sites[${this.config.mainSiteId}].settings[system]`, {
        systemInitialization: true
      });
      // ✅ CORRECCIÓN: Usar get directo con bypass de cache para obtener site
      const mainSite = await this.hyperContext.get(`sites[${this.config.mainSiteId}]`, {
        systemInitialization: true,
        bypassCache: true // ✅ CORRECCIÓN CRÍTICA: Forzar bypass de cache
      });

      console.log('🔍 [SystemInitializer] getSystemStatus - mainSite ownership:', mainSite?.ownership);

      return {
        initialized: !!siteSettings?.system?.initialized,
        mainSiteExists: !!mainSite,
        firstUserAssigned: !!mainSite?.ownership?.owner,
        version: siteSettings?.system?.version || 'unknown',
        initializedAt: siteSettings?.system?.initializedAt || null
      };
    } catch (error) {
      console.error('❌ Error obteniendo estado del sistema:', error);
      return {
        initialized: false,
        mainSiteExists: false,
        firstUserAssigned: false,
        version: 'unknown',
        error: error.message
      };
    }
  }

  /**
   * Crear estructura de storage para un site
   * @param {string} siteId - ID del site
   * @private
   */
  async _createSiteStorageStructure(siteId) {
    try {
      console.log(`📁 SystemInitializer: Creando estructura de storage para site ${siteId}...`);

      // ✅ CORRECCIÓN: Solo crear archivos de cache necesarios, no _metadata.json
      console.log(`📁 Preparando estructura de cache para site ${siteId}...`);

      // ✅ CORRECCIÓN: Crear solo archivos de cache esenciales (sin redundancias)
      const initialCacheFiles = {
        [`sites/${siteId}/cache/entities/index.json`]: {
          entities: {},
          lastUpdated: new Date().toISOString(),
          note: "Cache de entity specs - se actualiza automáticamente"
        },
        [`sites/${siteId}/cache/pages/index.json`]: {
          pages: {},
          lastUpdated: new Date().toISOString(),
          note: "Cache de páginas - se actualiza automáticamente"
        }
        // NOTA: modules y branding están en siteDoc, no necesitan cache separado
        // NOTA: fieldsets se manejan por FieldSetsManager, no necesitan cache aquí
      };

      for (const [path, content] of Object.entries(initialCacheFiles)) {
        try {
          console.log(`📄 Creando archivo de cache: ${path}`);
          await this.hyperContext.saveToStorage(path, content);
          console.log(`✅ Archivo de cache creado: ${path}`);
        } catch (error) {
          console.warn(`⚠️ Error creando archivo de cache ${path}:`, error.message);
          // Continuar con otros archivos
        }
      }

      console.log(`✅ Estructura de storage creada para site ${siteId}`);
    } catch (error) {
      console.error(`❌ Error creando estructura de storage para site ${siteId}:`, error);
      // No lanzar error para no interrumpir la creación del site
    }
  }

  /**
   * Crear páginas por defecto como subcolección
   * @param {string} siteId - ID del site
   * @private
   */
  async _createDefaultPages(siteId) {
    try {
      console.log(`📄 SystemInitializer: Creando páginas por defecto para site ${siteId}...`);

      // Definir páginas por defecto
      const defaultPages = {
        home: {
          id: 'home',
          title: 'Inicio',
          slug: 'home',
          path: '/',
          type: 'public',
          status: 'published',
          content: {
            title: 'Bienvenido a tu sitio',
            description: 'Esta es la página principal de tu sitio web.',
            sections: [
              {
                type: 'hero',
                title: 'Bienvenido',
                subtitle: 'Tu sitio está listo para usar',
                cta: { text: 'Comenzar', link: '/panel-dashboard' }
              }
            ]
          },
          seo: {
            title: 'Inicio',
            description: 'Página principal del sitio',
            keywords: ['inicio', 'home', 'principal']
          },
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'system',
            isSystemGenerated: true
          }
        },

        about: {
          id: 'about',
          title: 'Acerca de',
          slug: 'about',
          path: '/about',
          type: 'public',
          status: 'published',
          content: {
            title: 'Acerca de nosotros',
            description: 'Información sobre nuestro sitio y servicios.',
            sections: [
              {
                type: 'content',
                title: 'Nuestra historia',
                content: 'Aquí puedes agregar información sobre tu empresa o proyecto.'
              }
            ]
          },
          seo: {
            title: 'Acerca de',
            description: 'Información sobre nosotros',
            keywords: ['about', 'acerca', 'información']
          },
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'system',
            isSystemGenerated: true
          }
        },

        'panel-dashboard': {
          id: 'panel-dashboard',
          title: 'Panel de Control',
          slug: 'panel-dashboard',
          path: '/panel-dashboard',
          type: 'private',
          status: 'published',
          content: {
            title: 'Panel de Control',
            description: 'Administra tu sitio desde aquí.',
            sections: [
              {
                type: 'dashboard',
                widgets: ['stats', 'recent-activity', 'quick-actions']
              }
            ]
          },
          permissions: {
            view: ['admin', 'editor'],
            edit: ['admin']
          },
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'system',
            isSystemGenerated: true
          }
        },

        contact: {
          id: 'contact',
          title: 'Contacto',
          slug: 'contact',
          path: '/contact',
          type: 'public',
          status: 'published',
          content: {
            title: 'Contáctanos',
            description: 'Ponte en contacto con nosotros.',
            sections: [
              {
                type: 'contact-form',
                fields: ['name', 'email', 'message']
              }
            ]
          },
          seo: {
            title: 'Contacto',
            description: 'Página de contacto',
            keywords: ['contacto', 'contact', 'comunicación']
          },
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'system',
            isSystemGenerated: true
          }
        }
      };

      // ✅ CORRECCIÓN: Crear cada página en la subcolección pages/ con path correcto
      for (const [pageId, pageData] of Object.entries(defaultPages)) {
        console.log(`📄 Creando página: ${pageId}`);
        // ✅ CORRECCIÓN: Usar sintaxis correcta de HyperContext con punto (.) no slash (/)
        await this.hyperContext.set(`sites[${siteId}].pages[${pageId}]`, pageData, {
          systemInitialization: true
        });
        console.log(`✅ Página creada: ${pageId}`);
      }

      console.log(`✅ Páginas por defecto creadas para site ${siteId}: ${Object.keys(defaultPages).join(', ')}`);
    } catch (error) {
      console.error(`❌ Error creando páginas por defecto para site ${siteId}:`, error);
      // No lanzar error para no interrumpir la creación del site
    }
  }

  /**
   * ✅ NUEVO: Inicializar subcollections que antes eran arrays en el documento main
   */
  async _initializeMainSiteSubcollections(siteId) {
    try {
      console.log(`📊 Inicializando subcollections para site ${siteId}...`);

      // Inicializar subcollections de ownership - SUBCOLLECTIONS REALES
      const ownershipSubcollections = [
        'ownershipCollaborators',
        'ownershipTransferHistory',
        'ownershipInvitations',
        'ownershipRecords'
      ];

      for (const subcollection of ownershipSubcollections) {
        // Crear documento de inicialización para cada subcollection
        const initDoc = {
          _initialized: true,
          _createdAt: new Date().toISOString(),
          _createdBy: 'SystemInitializer',
          _type: 'initialization_marker'
        };

        await this.hyperContext.set(`sites[${siteId}].${subcollection}[_init]`, initDoc, {
          systemInitialization: true
        });
        console.log(`✅ Subcollection inicializada: ${subcollection}`);
      }

      // Inicializar subcollections de billing - SUBCOLLECTIONS REALES
      const billingSubcollections = [
        'billingHistory',
        'billingInvoices'
      ];

      for (const subcollection of billingSubcollections) {
        const initDoc = {
          _initialized: true,
          _createdAt: new Date().toISOString(),
          _createdBy: 'SystemInitializer',
          _type: 'initialization_marker'
        };

        await this.hyperContext.set(`sites[${siteId}].${subcollection}[_init]`, initDoc, {
          systemInitialization: true
        });
        console.log(`✅ Subcollection inicializada: ${subcollection}`);
      }

      console.log(`✅ Todas las subcollections inicializadas para site ${siteId}`);
    } catch (error) {
      console.error(`❌ Error inicializando subcollections para site ${siteId}:`, error);
      // No lanzar error para no interrumpir la creación del site
    }
  }

  /**
   * ✅ NUEVO: Cargar y cachear datos iniciales del site
   */
  async _loadAndCacheInitialSiteData(siteId) {
    try {
      console.log(`📊 Cargando y cacheando datos iniciales del site ${siteId}...`);

      // Usar el nuevo método de HyperContextEnhanced para cargar todos los datos
      const completeSiteData = await this.hyperContext.loadAndCacheCompleteSiteData(siteId);

      if (completeSiteData) {
        console.log(`✅ Datos iniciales del site ${siteId} cargados y cacheados exitosamente`);

        // ✅ CORRECCIÓN: Crear cache solo para datos que no están en siteDoc

        // ✅ REFACTORIZADO: Cache para specs unificados (no está en siteDoc)
        if (completeSiteData.specs) {
          await this.hyperContext.saveToStorage(
            `sites/${siteId}/cache/specs/all.json`,
            completeSiteData.specs
          );
        }

        // ✅ LEGACY: Cache para entity specs (compatibility)
        if (completeSiteData.entitySpecs) {
          await this.hyperContext.saveToStorage(
            `sites/${siteId}/cache/entities/specs.json`,
            completeSiteData.entitySpecs
          );
        }

        // Cache para páginas (no está en siteDoc)
        if (completeSiteData.pages) {
          await this.hyperContext.saveToStorage(
            `sites/${siteId}/cache/pages/index.json`,
            completeSiteData.pages
          );
        }

        // NOTA: moduleConfigs y brandingConfig ya están en siteDoc, no necesitan cache separado

        return completeSiteData;
      } else {
        console.warn(`⚠️ No se pudieron cargar datos completos del site ${siteId}`);
        return null;
      }

    } catch (error) {
      console.error(`❌ Error cargando datos iniciales del site ${siteId}:`, error);
      // No lanzar error para no interrumpir la creación del site
      return null;
    }
  }

  /**
   * Crear sesión completa para usuario siguiendo el big-picture corregido
   */
  async createUserSession({ userId, siteId = 'main', firebaseUser, isFirstUser = false }) {
    try {
      console.log('🔄 [SystemInitializer] Creando sesión para usuario:', userId, 'en site:', siteId);

      // ✅ PASO 1: Verificar/crear perfil global del usuario
      console.log('🔧 [SystemInitializer] Verificando perfil global del usuario...');
      let globalUser = await this.hyperContext.get(`users[${userId}]`);

      if (!globalUser) {
        console.log('🆕 [SystemInitializer] Creando perfil global del usuario...');

        // Crear perfil global independiente de cualquier site
        const globalUserData = {
          uid: userId,
          email: firebaseUser?.email || null,
          displayName: firebaseUser?.displayName || firebaseUser?.email?.split('@')[0] || 'Usuario',
          photoURL: firebaseUser?.photoURL || null,
          emailVerified: firebaseUser?.emailVerified || false,

          // Datos personales privados
          profile: {
            firstName: firebaseUser?.displayName?.split(' ')[0] || '',
            lastName: firebaseUser?.displayName?.split(' ').slice(1).join(' ') || '',
            language: 'es',
            timezone: 'America/Mexico_City'
          },

          // Preferencias globales
          preferences: {
            theme: 'auto',
            notifications: true,
            privacy: 'private'
          },

          // ✅ REFACTORIZACIÓN: siteAccess ahora es una subcollection
          // NOTA: siteAccess se inicializa como subcollection en UserManager.initializeUserSubcollections()
          // Path: users[userId].siteAccess[main]

          // Metadatos
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 'SystemInitializer',
            isFirstUser,
            lastLoginAt: new Date().toISOString()
          }
        };

        await this.hyperContext.set(`users[${userId}]`, globalUserData);
        globalUser = globalUserData;
        console.log('✅ [SystemInitializer] Perfil global creado');
      } else {
        // Actualizar última conexión y marcar como primer usuario si aplica
        globalUser.metadata.lastLoginAt = new Date().toISOString();
        globalUser.metadata.updatedAt = new Date().toISOString();
        if (isFirstUser) {
          globalUser.metadata.isFirstUser = true;
        }
        await this.hyperContext.set(`users[${userId}]`, globalUser);
        console.log('✅ [SystemInitializer] Perfil global actualizado');
      }

      // ✅ CORRECCIÓN: Actualizar o crear credencial para el site (evitar duplicaciones)
      console.log('🔧 [SystemInitializer] Actualizando o creando credencial para el site...');

      const credentialData = {
        // Roles y permisos
        roles: isFirstUser ? ['super_admin'] : ['user'],
        permissions: isFirstUser ? { '*': true } : {},

        // Estado de la credencial
        status: {
          isActive: true,
          isVerified: true,
          isPrimary: true,
          expiresAt: null
        },

        // Información adicional
        isOwner: isFirstUser, // ✅ Control de ownership del site
        name: isFirstUser ? 'Administrador Principal' : 'Usuario Estándar',
        description: isFirstUser ? 'Acceso completo al site con permisos administrativos' : 'Acceso básico al site',

        // Metadatos de la operación
        updatedBy: 'SystemInitializer',
        reason: isFirstUser ? 'first_user_super_admin_assignment' : 'standard_user_registration',

        // Metadatos de la credencial
        metadata: {
          isFirstUser,
          assignmentReason: isFirstUser ? 'first_user_super_admin' : 'standard_user',
          assignmentContext: 'system_initialization',
          lastUsed: new Date().toISOString(),
          createdBy: 'SystemInitializer',
          lastModifiedBy: 'SystemInitializer'
          // NOTA: changeHistory es SUBCOLLECTION REAL
          // Path: sites[siteId].credentials[credentialId].changeHistory[]
        }
      };

      // ✅ CORRECCIÓN: Usar updateOrCreateCredential para evitar duplicaciones
      const userManager = new (require('./UserManager.js'))(this.hyperContext, this.admin);
      const credential = await userManager.updateOrCreateCredential(userId, siteId, credentialData);
      console.log('✅ [SystemInitializer] Credencial actualizada/creada:', credential.id);

      // ✅ ARQUITECTURA LEGACY: Los permisos están embebidos en los roles, no como entidades separadas
      console.log('✅ [SystemInitializer] Permisos embebidos en roles (arquitectura legacy)');

      // ✅ NUEVO: Inicializar módulos por defecto para el site
      await this.initializeSiteModules(siteId);

      // ✅ NUEVO: Inicializar codeblocks por defecto para el site
      await this.initializeSiteCodeBlocks(siteId);

      // ✅ PASO 3: Crear perfil del usuario en el site (CORREGIDO: usar sites[].profiles[])
      console.log('🔧 [SystemInitializer] Creando perfil del usuario en el site...');
      const siteProfileData = {
        userId,
        siteId,

        // Datos específicos del site
        siteSpecificData: {
          displayName: globalUser.displayName,
          avatar: globalUser.photoURL,
          bio: isFirstUser ? 'Administrador principal del sistema' : '',
          department: isFirstUser ? 'Administración' : '',
          position: isFirstUser ? 'Super Admin' : 'Usuario',
          startDate: new Date().toISOString().split('T')[0],
          employeeId: isFirstUser ? 'ADMIN-001' : null,

          sitePreferences: {
            notifications: {
              projectUpdates: true,
              mentions: true,
              deadlines: true
            },
            workingHours: {
              start: '09:00',
              end: '18:00',
              timezone: 'America/Mexico_City',
              workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
            },
            dashboard: {
              defaultView: 'projects',
              widgets: ['tasks', 'calendar', 'notifications']
            }
          }
        },

        // ✅ PROYECTOR: Referencia simple al rol
        roleId: credentialData.roleId,
        isOwner: credentialData.isOwner,

        // Estado en el site
        status: {
          isActive: true,
          isVerified: true,
          isPending: false,
          lastActivity: new Date().toISOString(),
          joinedAt: new Date().toISOString()
        },

        // Metadatos
        metadata: {
          invitedBy: isFirstUser ? 'SystemInitializer' : null,
          invitedAt: new Date().toISOString(),
          approvedBy: isFirstUser ? 'SystemInitializer' : null,
          approvedAt: new Date().toISOString(),
          isFirstUser,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          version: '1.2.0'
        }
      };

      // ✅ CORRECCIÓN CRÍTICA: Guardar perfil en sites[siteId].profiles[userId]
      await this.hyperContext.set(`sites[${siteId}].profiles[${userId}]`, siteProfileData);
      console.log('✅ [SystemInitializer] Perfil del site creado en sites[].profiles[]');

      // ✅ PASO 4: Sincronizar referencias de sites en usuario global
      console.log('🔧 [SystemInitializer] Sincronizando referencias de sites...');
      await this.syncUserSiteAccess(userId, siteId);

      return {
        success: true,
        globalUser,
        credential: credentialData,
        siteProfile: siteProfileData,
        isFirstUser
      };

    } catch (error) {
      console.error('❌ [SystemInitializer] Error creando sesión de usuario:', error);
      throw error;
    }
  }

  /**
   * ✅ REFACTORIZACIÓN: Sincronizar referencias de sites usando subcollection
   */
  async syncUserSiteAccess(userId, siteId) {
    try {
      console.log('🔄 [SystemInitializer] Sincronizando acceso a sites para usuario:', userId);

      // ✅ CORRECCIÓN: Usar la instancia existente de UserManager en lugar de crear una nueva
      const userManager = this.userManager;

      // Agregar site al acceso del usuario
      await userManager.updateUserSiteAccess(userId, siteId, 'add');

      // Marcar como site por defecto si es el primero
      const currentAccess = await userManager.getUserSiteAccess(userId);
      if (!currentAccess.defaultSite) {
        await userManager.updateUserSiteAccess(userId, siteId, 'visit');
      }

      console.log('✅ [SystemInitializer] Acceso a sites sincronizado correctamente usando subcollection');
      return await userManager.getUserSiteAccess(userId);

    } catch (error) {
      console.error('❌ [SystemInitializer] Error sincronizando acceso a sites:', error);
      throw error;
    }
  }

  /**
   * ✅ ARQUITECTURA LEGACY: Los permisos están embebidos en los roles
   * No necesitamos inicializar permisos separados como en el sistema legacy
   */

  /**
   * ✅ NUEVO: Inicializar módulos por defecto para un site
   * @param {string} siteId - ID del site
   * @returns {Promise<boolean>} - Éxito de la inicialización
   */
  async initializeSiteModules(siteId) {
    try {
      console.log(`📦 [SystemInitializer] Inicializando módulos para site: ${siteId}`);

      const defaultModules = [
        {
          id: 'entity',
          name: 'Entity Manager',
          version: '1.0.0',
          description: 'Gestión de entidades del site',
          category: 'core',
          isActive: true,
          config: {
            dependencies: [],
            permissions: ['entities_read', 'entities_write'],
            settings: {
              enableValidation: true,
              enableAudit: true
            }
          }
        },
        {
          id: 'pages',
          name: 'Pages Manager',
          version: '1.0.0',
          description: 'Gestión de páginas del site',
          category: 'content',
          isActive: true,
          config: {
            dependencies: ['entity'],
            permissions: ['pages_read', 'pages_write'],
            settings: {
              enableSEO: true,
              enableVersioning: true
            }
          }
        },
        {
          id: 'user',
          name: 'User Manager',
          version: '1.0.0',
          description: 'Gestión de usuarios del site',
          category: 'core',
          isActive: true,
          config: {
            dependencies: [],
            permissions: ['site_admin'],
            settings: {
              enableProfiles: true,
              enableCredentials: true
            }
          }
        }
      ];

      // Crear módulos por defecto
      for (const module of defaultModules) {
        const moduleData = {
          ...module,
          siteId,
          installation: {
            installedAt: new Date().toISOString(),
            installedBy: 'SystemInitializer',
            configuredAt: new Date().toISOString(),
            status: 'active'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await this.hyperContext.set(`sites[${siteId}].modules[${module.id}]`, moduleData);
      }

      console.log(`✅ [SystemInitializer] ${defaultModules.length} módulos inicializados para site: ${siteId}`);
      return true;
    } catch (error) {
      console.error(`❌ [SystemInitializer] Error inicializando módulos para site ${siteId}:`, error);
      return false;
    }
  }

  /**
   * ✅ NUEVO: Inicializar codeblocks por defecto para un site
   * @param {string} siteId - ID del site
   * @returns {Promise<boolean>} - Éxito de la inicialización
   */
  async initializeSiteCodeBlocks(siteId) {
    try {
      console.log(`🧩 [SystemInitializer] Inicializando codeblocks para site: ${siteId}`);

      const defaultCodeBlocks = [
        {
          id: 'hello_world',
          name: 'Hello World',
          type: 'component',
          category: 'example',
          description: 'CodeBlock de ejemplo básico',
          version: '1.0.0',
          isActive: true,
          config: {
            framework: 'react',
            dependencies: [],
            permissions: ['codeblocks_execute'],
            settings: {
              enableCache: true
            }
          },
          code: {
            javascript: 'export default function HelloWorld() { return <div>Hello World!</div>; }',
            css: '.hello-world { color: blue; }',
            html: '<div class="hello-world">Hello World!</div>',
            json: '{"message": "Hello World!"}'
          },
          security: {
            validatedAt: new Date().toISOString(),
            validatedBy: 'SystemInitializer',
            securityLevel: 'low',
            allowedPaths: ['sites[' + siteId + ']'],
            restrictions: {
              maxExecutionTime: 5000,
              maxMemoryUsage: '10MB'
            }
          }
        }
      ];

      // Crear codeblocks por defecto
      for (const codeblock of defaultCodeBlocks) {
        const codeblockData = {
          ...codeblock,
          siteId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await this.hyperContext.set(`sites[${siteId}].codeblocks[${codeblock.id}]`, codeblockData);
      }

      console.log(`✅ [SystemInitializer] ${defaultCodeBlocks.length} codeblocks inicializados para site: ${siteId}`);
      return true;
    } catch (error) {
      console.error(`❌ [SystemInitializer] Error inicializando codeblocks para site ${siteId}:`, error);
      return false;
    }
  }

  /**
   * ✅ ARQUITECTURA CORRECTA: Inicializar roles dinámicos del sistema
   * Roles editables mediante CRUD visual con estructura legacy-compatible
   */
  async _initializeSystemRoles(siteId) {
    try {
      console.log(`🔧 [SystemInitializer] Inicializando roles dinámicos para site: ${siteId}`);

      // ✅ PROYECTOR: Usar roles por defecto desde configuración
      const defaultRoles = DEFAULT_PROYECTOR_ROLES;

      // ✅ PROYECTOR: Usar RoleManager para inicializar roles por defecto
      const success = await this.roleManager.initializeDefaultRoles(siteId);

      if (!success) {
        console.warn(`⚠️ [SystemInitializer] Algunos roles no se pudieron inicializar para site: ${siteId}`);
      }

      console.log(`✅ [SystemInitializer] Roles dinámicos inicializados para site: ${siteId}`);
      return success;
    } catch (error) {
      console.error(`❌ [SystemInitializer] Error inicializando roles dinámicos para site ${siteId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ NUEVO: Migrar rutas legacy a arquitectura site-specific
   * Limpia las rutas globales incorrectas /settings/settings y /permissions/permissions
   */
  async migrateLegacyRoutes() {
    try {
      console.log('🔄 [SystemInitializer] Migrando rutas legacy a arquitectura site-specific...');

      const legacyPaths = [
        'settings/settings',
        'permissions/permissions'
      ];

      for (const legacyPath of legacyPaths) {
        try {
          // Verificar si existe la ruta legacy
          const legacyData = await this.hyperContext.get(legacyPath);

          if (legacyData) {
            console.log(`🗑️ [SystemInitializer] Eliminando ruta legacy: ${legacyPath}`);

            // Eliminar la ruta legacy
            await this.hyperContext.delete(legacyPath);

            console.log(`✅ [SystemInitializer] Ruta legacy eliminada: ${legacyPath}`);
          } else {
            console.log(`ℹ️ [SystemInitializer] Ruta legacy no existe: ${legacyPath}`);
          }
        } catch (error) {
          console.warn(`⚠️ [SystemInitializer] Error procesando ruta legacy ${legacyPath}:`, error.message);
          // Continuar con la siguiente ruta
        }
      }

      console.log('✅ [SystemInitializer] Migración de rutas legacy completada');
      return true;
    } catch (error) {
      console.error('❌ [SystemInitializer] Error en migración de rutas legacy:', error);
      throw error;
    }
  }
}