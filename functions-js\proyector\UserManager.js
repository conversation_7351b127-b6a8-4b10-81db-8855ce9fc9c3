/**
 * User Manager
 *
 * Sistema unificado de gestión de usuarios
 * Maneras del Proyector: usuarios globales con perfiles por site
 *
 * Inspirado en AuthContext legacy pero con arquitectura moderna:
 * - Usuarios globales con datos privados/públicos separados
 * - Perfiles específicos por site
 * - Credenciales por site con roles múltiples
 * - Integración con Firebase Auth
 */

export class UserManager {
  constructor({ hyperContext, config = {} }) {
    if (!hyperContext) {
      throw new Error("HyperContext Enhanced es requerido");
    }

    this.hyperContext = hyperContext;
    this.config = config;
    // Cache storage-based (NO memory cache)
    this.initialized = false;

    // PERFORMANCE: Inicializar PerformanceMonitor
    this.performanceMonitor = null;
  }

  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      console.log('Inicializando UserManager...');

      // Validar conexión con HyperContext
      if (!this.hyperContext.initialized) {
        throw new Error('HyperContext debe estar inicializado antes que UserManager');
      }

      // ✅ PERFORMANCE: Inicializar PerformanceMonitor
      await this.initializePerformanceMonitor();

      this.initialized = true;
      console.log('UserManager inicializado correctamente');
    } catch (error) {
      console.error('Error inicializando UserManager:', error);
      throw error;
    }
  }

  /**
   * Crear usuario global nuevo
   * @param {Object} userData - Datos del usuario
   * @param {Object} firebaseUser - Usuario de Firebase Auth
   * @returns {Promise<Object>} - Usuario global creado
   */
  async createGlobalUser(userData, firebaseUser) {
    if (!userData || !firebaseUser) {
      throw new Error("userData y firebaseUser son requeridos");
    }

    try {
      const userId = firebaseUser.uid;
      const now = new Date().toISOString();

      // ✅ REFACTORIZACIÓN: Crear estructura optimizada con documentos individuales

      // Documento base del usuario (datos mínimos)
      const globalUser = {
        id: userId,
        email: firebaseUser.email,
        status: 'active',
        createdAt: now,
        updatedAt: now
      };

      // Perfil público como documento individual
      const publicProfile = {
        displayName: userData.displayName || firebaseUser.displayName || '',
        avatar: userData.avatar || firebaseUser.photoURL || '',
        bio: userData.bio || '',
        website: userData.website || '',
        location: userData.location || '',
        timezone: userData.timezone || 'America/Mexico_City',
        language: userData.language || 'es',
        createdAt: now,
        updatedAt: now
      };

      // Datos privados como documento individual
      const privateData = {
        email: firebaseUser.email,
        emailVerified: firebaseUser.emailVerified,
        phoneNumber: firebaseUser.phoneNumber || userData.phoneNumber || '',
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        dateOfBirth: userData.dateOfBirth || '',
        preferences: {
          notifications: {
            email: true,
            push: true,
            sms: false
          },
          privacy: {
            profileVisibility: 'public',
            showEmail: false,
            showPhone: false
          },
          ui: {
            theme: 'light',
            language: 'es'
          }
        },
        security: {
          twoFactorEnabled: false,
          lastPasswordChange: now
          // NOTA: loginHistory es una subcollection: users[userId].loginHistory[]
        },
        createdAt: now,
        updatedAt: now
      };

      // Metadatos del sistema como documento individual
      const metadata = {
        provider: firebaseUser.providerData[0]?.providerId || 'email',
        lastLoginAt: now,
        isActive: true,
        accountStatus: 'active',
        createdBy: 'system',
        version: '1.0.0'
      };

      // ✅ CORRECCIÓN: Usar métodos correctos con systemInitialization para bypass de autenticación
      await this.hyperContext.setGlobalEntity('users', userId, globalUser, { systemInitialization: true });
      await this.hyperContext.set(`users[${userId}].publicProfile`, publicProfile);
      await this.hyperContext.set(`users[${userId}].privateData`, privateData);
      await this.hyperContext.set(`users[${userId}].metadata`, metadata);

      // ✅ NUEVO: Inicializar subcollections del usuario
      await this.initializeUserSubcollections(userId);

      // ✅ CORRECCIÓN: Crear perfil de site después de que el usuario global existe
      await this.createSiteProfile(userId, 'main', {
        displayName: userData.displayName || firebaseUser.displayName || 'Usuario',
        bio: '',
        avatar: firebaseUser.photoURL || null,
        preferences: {},
        customFields: {},
        createdBy: 'system'
      });

      // ✅ CORRECCIÓN: Crear credencial después del perfil
      await this.createSiteCredential(userId, 'main', {
        roles: ['user'],
        permissions: {},
        isActive: true,
        isPrimary: true,
        createdBy: 'system',
        reason: 'user_registration'
      });

      console.log(`Usuario global creado con estructura optimizada: ${userId}`);

      // Retornar estructura completa para compatibilidad
      return {
        ...globalUser,
        publicProfile,
        privateData,
        metadata
      };

    } catch (error) {
      console.error(`Error creando usuario global:`, error);
      throw error;
    }
  }

  /**
   * Crear perfil de usuario en un site específico
   * @param {string} userId - ID del usuario global
   * @param {string} siteId - ID del site
   * @param {Object} profileData - Datos del perfil en el site
   * @returns {Promise<Object>} - Perfil del site creado
   */
  async createSiteProfile(userId, siteId, profileData = {}) {
    if (!userId || !siteId) {
      throw new Error("userId y siteId son requeridos");
    }

    try {
      // Verificar que el usuario global existe (con systemInitialization para casos de creación)
      const globalUser = await this.getGlobalUser(userId, { systemInitialization: true });
      if (!globalUser) {
        throw new Error(`Usuario global ${userId} no existe`);
      }

      const now = new Date().toISOString();

      // Crear perfil específico del site
      const siteProfile = {
        userId,
        siteId,

        // Perfil en el site (puede diferir del perfil global)
        profile: {
          displayName: profileData.displayName || globalUser.publicProfile.displayName,
          avatar: profileData.avatar || globalUser.publicProfile.avatar,
          bio: profileData.bio || '',
          role: profileData.role || 'user',
          department: profileData.department || '',
          position: profileData.position || '',
          joinedAt: now,
          isActive: true
        },

        // Configuración específica del site
        siteConfig: {
          notifications: profileData.notifications || {
            email: true,
            push: true
          },
          permissions: profileData.permissions || {},
          preferences: profileData.preferences || {},
          customFields: profileData.customFields || {}
        },

        // Metadatos
        metadata: {
          createdAt: now,
          updatedAt: now,
          createdBy: profileData.createdBy || 'system',
          invitedBy: profileData.invitedBy || null,
          status: 'active'
        }
      };

      // ✅ CORRECCIÓN: Guardar perfil en sites[siteId].profiles[userId]
      await this.hyperContext.set(`sites[${siteId}].profiles[${userId}]`, siteProfile);

      console.log(`Perfil de site creado: ${userId} en ${siteId}`);
      return siteProfile;

    } catch (error) {
      console.error(`Error creando perfil de site para ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ CORRECCIÓN: Actualizar o crear credencial de usuario en un site
   * Evita duplicaciones - cada usuario debe tener UNA sola credencial por site
   * @param {string} userId - ID del usuario global
   * @param {string} siteId - ID del site
   * @param {Object} credentialData - Datos de la credencial
   * @returns {Promise<Object>} - Credencial actualizada o creada
   */
  async updateOrCreateCredential(userId, siteId, credentialData) {
    if (!userId || !siteId || !credentialData) {
      throw new Error("userId, siteId y credentialData son requeridos");
    }

    try {
      // ✅ PASO 1: Verificar si ya existe una credencial para este usuario en este site
      const existingCredential = await this.getSiteCredential(userId, siteId);

      if (existingCredential) {
        console.log(`🔄 [UserManager] Actualizando credencial existente para ${userId} en ${siteId}`);

        // Actualizar credencial existente
        const updatedCredential = {
          ...existingCredential,
          ...credentialData,

          // Preservar datos críticos
          id: existingCredential.id,
          userId: existingCredential.userId,
          siteId: existingCredential.siteId,

          // Actualizar metadatos
          metadata: {
            ...existingCredential.metadata,
            ...credentialData.metadata,
            updatedAt: new Date().toISOString(),
            lastModifiedBy: credentialData.updatedBy || 'system',
            lastModifiedAt: new Date().toISOString(),
            version: (existingCredential.metadata?.version || 1) + 1
          }
        };

        // Guardar credencial actualizada
        await this.hyperContext.set(`sites[${siteId}].credentials[${existingCredential.id}]`, updatedCredential);

        // ✅ AUDITORÍA: Registrar actualización de credencial
        await this.addCredentialChangeEntry(siteId, existingCredential.id, {
          changedBy: credentialData.updatedBy || 'system',
          changes: ['credential_updated'],
          reason: credentialData.reason || 'credential_update',
          previousValues: {
            roles: existingCredential.roles,
            permissions: existingCredential.permissions,
            status: existingCredential.status
          },
          newValues: {
            roles: updatedCredential.roles,
            permissions: updatedCredential.permissions,
            status: updatedCredential.status
          },
          affectedPermissions: Object.keys(updatedCredential.permissions || {})
        });

        console.log(`✅ [UserManager] Credencial actualizada: ${existingCredential.id}`);
        return updatedCredential;
      } else {
        console.log(`🆕 [UserManager] Creando nueva credencial para ${userId} en ${siteId}`);

        // Crear nueva credencial usando la función existente
        return await this.createSiteCredential(userId, siteId, credentialData);
      }
    } catch (error) {
      console.error(`❌ [UserManager] Error en updateOrCreateCredential para ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Crear credencial de usuario en un site
   * @param {string} userId - ID del usuario global
   * @param {string} siteId - ID del site
   * @param {Object} credentialData - Datos de la credencial
   * @returns {Promise<Object>} - Credencial creada
   */
  async createSiteCredential(userId, siteId, credentialData) {
    if (!userId || !siteId || !credentialData) {
      throw new Error("userId, siteId y credentialData son requeridos");
    }

    try {
      // Verificar que el perfil del site existe
      const siteProfile = await this.getSiteProfile(userId, siteId);
      if (!siteProfile) {
        throw new Error(`Perfil de site ${userId}:${siteId} no existe`);
      }

      const now = new Date().toISOString();
      // ✅ CORRECCIÓN: ID único sin redundancia del siteId (ya está en el path)
      const credentialId = `${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

      // Crear credencial
      const credential = {
        id: credentialId,
        userId,
        siteId,

        // Roles en el site (pueden ser múltiples)
        roles: credentialData.roles || ['user'],

        // Permisos específicos (además de los heredados de roles)
        permissions: credentialData.permissions || {},

        // Estado de la credencial
        status: {
          isActive: credentialData.isActive !== false,
          isVerified: credentialData.isVerified || false,
          isPrimary: credentialData.isPrimary || false,
          expiresAt: credentialData.expiresAt || null
        },

        // Configuración de acceso
        access: {
          allowedIPs: credentialData.allowedIPs || [],
          allowedDevices: credentialData.allowedDevices || [],
          sessionTimeout: credentialData.sessionTimeout || 24 * 60 * 60, // 24 horas
          requireTwoFactor: credentialData.requireTwoFactor || false
        },

        // Metadatos
        metadata: {
          createdAt: now,
          updatedAt: now,
          createdBy: credentialData.createdBy || 'system',
          grantedBy: credentialData.grantedBy || null,
          reason: credentialData.reason || 'user_registration'
        }
      };

      // ✅ CORRECCIÓN: Guardar credencial en sites[siteId].credentials[credentialId]
      await this.hyperContext.set(`sites[${siteId}].credentials[${credentialId}]`, credential);

      // ✅ AUDITORÍA: Registrar creación de credencial
      await this.addCredentialChangeEntry(siteId, credentialId, {
        changedBy: credentialData.createdBy || 'system',
        changes: ['credential_created'],
        reason: credentialData.reason || 'user_registration',
        previousValues: {},
        newValues: {
          roles: credential.roles,
          permissions: credential.permissions,
          status: credential.status
        },
        affectedPermissions: Object.keys(credential.permissions || {})
      });

      console.log(`Credencial de site creada: ${credentialId}`);
      return credential;

    } catch (error) {
      console.error(`Error creando credencial de site para ${userId}:${siteId}:`, error);
      throw error;
    }
  }
  /**
   * Obtener usuario global
   * @param {string} userId - ID del usuario
   * @param {Object} options - Opciones adicionales
   * @returns {Promise<Object|null>} - Usuario global o null
   */
  async getGlobalUser(userId, options = {}) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      // ✅ CORRECCIÓN: Determinar si necesita bypass de autenticación
      const needsSystemAccess = options.systemInitialization ||
        (this.hyperContext.userAuth?.uid === userId); // Usuario accediendo a sus propios datos

      const accessOptions = needsSystemAccess ? { systemInitialization: true } : {};

      // ✅ CORRECCIÓN: Usar acceso directo para evitar problemas de consistencia
      console.log(`🔍 getGlobalUser: Buscando usuario ${userId} con opciones:`, accessOptions);

      const [baseUser, publicProfile, privateData, metadata] = await Promise.all([
        this.hyperContext.get(`users[${userId}]`, accessOptions),
        this.hyperContext.get(`users[${userId}].publicProfile`),
        this.hyperContext.get(`users[${userId}].privateData`),
        this.hyperContext.get(`users[${userId}].metadata`)
      ]);

      console.log(`🔍 getGlobalUser resultados:`, {
        baseUser: baseUser ? 'ENCONTRADO' : 'NO ENCONTRADO',
        publicProfile: publicProfile ? 'ENCONTRADO' : 'NO ENCONTRADO',
        privateData: privateData ? 'ENCONTRADO' : 'NO ENCONTRADO',
        metadata: metadata ? 'ENCONTRADO' : 'NO ENCONTRADO'
      });

      if (!baseUser) {
        console.log(`❌ getGlobalUser: Usuario base no encontrado para ${userId}`);
        return null;
      }

      // ✅ MIGRACIÓN AUTOMÁTICA: Si no existen documentos individuales, migrar
      if (!publicProfile || !privateData || !metadata) {
        console.log(`Detectada estructura antigua para usuario ${userId}, migrando...`);
        const migrated = await this.migrateUserToOptimizedStructure(userId);
        if (migrated) {
          // ✅ CORRECCIÓN: Evitar recursión infinita - obtener datos directamente después de migración
          const [retryPublicProfile, retryPrivateData, retryMetadata] = await Promise.all([
            this.hyperContext.get(`users[${userId}].publicProfile`),
            this.hyperContext.get(`users[${userId}].privateData`),
            this.hyperContext.get(`users[${userId}].metadata`)
          ]);

          return {
            ...baseUser,
            publicProfile: retryPublicProfile || {},
            privateData: retryPrivateData || {},
            metadata: retryMetadata || {}
          };
        }
      }

      // Combinar documentos para compatibilidad con código existente
      return {
        ...baseUser,
        publicProfile: publicProfile || {},
        privateData: privateData || {},
        metadata: metadata || {}
      };
    } catch (error) {
      console.error(`Error obteniendo usuario global ${userId}:`, error);
      return null;
    }
  }

  /**
   * ✅ NUEVO: Inicializar subcollections del usuario
   * @param {string} userId - ID del usuario
   * @returns {Promise<boolean>} - Éxito de la inicialización
   */
  async initializeUserSubcollections(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      // Inicializar siteAccess con documento principal
      const defaultSiteAccess = {
        type: 'main',
        availableSites: [],
        defaultSite: null,
        recentSites: [],
        crossSiteSettings: {
          allowAutoSwitch: true,
          showAllSitesInMenu: true
        },
        lastSyncAt: new Date().toISOString(),
        syncVersion: 1
      };

      // ✅ CORRECCIÓN: Usar path correcto para subcollección de usuario
      await this.hyperContext.set(`users[${userId}].siteAccess[main]`, defaultSiteAccess);

      // NOTA: loginHistory se inicializa automáticamente cuando se agrega la primera entrada
      // No necesita inicialización explícita

      console.log(`Subcollections inicializadas para usuario: ${userId}`);
      return true;
    } catch (error) {
      console.error(`Error inicializando subcollections para ${userId}:`, error);
      return false;
    }
  }

  /**
   * ✅ NUEVO: Obtener solo perfil público del usuario
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object|null>} - Perfil público o null
   */
  async getUserPublicProfile(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const publicProfile = await this.hyperContext.get(`users[${userId}].publicProfile`);
      return publicProfile;
    } catch (error) {
      console.error(`Error obteniendo perfil público para ${userId}:`, error);
      return null;
    }
  }

  /**
   * ✅ NUEVO: Obtener solo datos privados del usuario
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object|null>} - Datos privados o null
   */
  async getUserPrivateData(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const privateData = await this.hyperContext.get(`users[${userId}].privateData`);
      return privateData;
    } catch (error) {
      console.error(`Error obteniendo datos privados para ${userId}:`, error);
      return null;
    }
  }

  /**
   * ✅ NUEVO: Obtener solo metadatos del usuario
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object|null>} - Metadatos o null
   */
  async getUserMetadata(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const metadata = await this.hyperContext.get(`users[${userId}].metadata`);
      return metadata;
    } catch (error) {
      console.error(`Error obteniendo metadatos para ${userId}:`, error);
      return null;
    }
  }

  /**
   * ✅ NUEVO: Actualizar perfil público del usuario
   * @param {string} userId - ID del usuario
   * @param {Object} profileData - Datos del perfil a actualizar
   * @returns {Promise<boolean>} - Éxito de la operación
   */
  async updateUserPublicProfile(userId, profileData) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const updatedProfile = {
        ...profileData,
        updatedAt: new Date().toISOString()
      };

      await this.hyperContext.set(`users[${userId}].publicProfile`, updatedProfile);
      console.log(`Perfil público actualizado para usuario: ${userId}`);
      return true;
    } catch (error) {
      console.error(`Error actualizando perfil público para ${userId}:`, error);
      return false;
    }
  }

  /**
   * ✅ NUEVO: Actualizar datos privados del usuario
   * @param {string} userId - ID del usuario
   * @param {Object} privateData - Datos privados a actualizar
   * @returns {Promise<boolean>} - Éxito de la operación
   */
  async updateUserPrivateData(userId, privateData) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const updatedData = {
        ...privateData,
        updatedAt: new Date().toISOString()
      };

      await this.hyperContext.set(`users[${userId}].privateData`, updatedData);
      console.log(`Datos privados actualizados para usuario: ${userId}`);
      return true;
    } catch (error) {
      console.error(`Error actualizando datos privados para ${userId}:`, error);
      return false;
    }
  }

  /**
   * ✅ NUEVO: Actualizar metadatos del usuario
   * @param {string} userId - ID del usuario
   * @param {Object} metadata - Metadatos a actualizar
   * @returns {Promise<boolean>} - Éxito de la operación
   */
  async updateUserMetadata(userId, metadata) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const updatedMetadata = {
        ...metadata,
        updatedAt: new Date().toISOString()
      };

      await this.hyperContext.set(`users[${userId}].metadata`, updatedMetadata);
      console.log(`Metadatos actualizados para usuario: ${userId}`);
      return true;
    } catch (error) {
      console.error(`Error actualizando metadatos para ${userId}:`, error);
      return false;
    }
  }

  /**
   * ✅ CORREGIDO: Migrar usuario existente a estructura optimizada
   * @param {string} userId - ID del usuario a migrar
   * @returns {Promise<boolean>} - Éxito de la migración
   */
  async migrateUserToOptimizedStructure(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      // ✅ CORRECCIÓN: Usar método correcto de HyperContextEnhanced con systemInitialization
      const oldUser = await this.hyperContext.accessGlobalEntity('users', userId, { systemInitialization: true });
      if (!oldUser) {
        console.log(`Usuario ${userId} no existe, no se requiere migración`);
        return false;
      }

      // ✅ CORRECCIÓN: Lógica invertida - si TIENE publicProfile/privateData entonces es estructura ANTIGUA
      if (oldUser.publicProfile || oldUser.privateData) {
        console.log(`Migrando usuario ${userId} de estructura antigua a optimizada...`);

        // Extraer datos de la estructura antigua
        const { publicProfile, privateData, metadata, ...baseUser } = oldUser;

        // ✅ CORRECCIÓN: Usar métodos correctos de HyperContextEnhanced con systemInitialization
        await Promise.all([
          this.hyperContext.setGlobalEntity('users', userId, {
            ...baseUser,
            updatedAt: new Date().toISOString()
          }, { systemInitialization: true }),
          this.hyperContext.set(`users[${userId}].publicProfile`, {
            ...(publicProfile || {}),
            updatedAt: new Date().toISOString()
          }),
          this.hyperContext.set(`users[${userId}].privateData`, {
            ...(privateData || {}),
            updatedAt: new Date().toISOString()
          }),
          this.hyperContext.set(`users[${userId}].metadata`, {
            ...(metadata || {}),
            updatedAt: new Date().toISOString()
          })
        ]);

        console.log(`Usuario ${userId} migrado exitosamente a estructura optimizada`);
        return true;
      } else {
        console.log(`Usuario ${userId} ya está en estructura optimizada`);
        return true;
      }
    } catch (error) {
      console.error(`Error migrando usuario ${userId}:`, error);
      return false;
    }
  }

  /**
   * ✅ REFACTORIZACIÓN: Obtener sites accesibles para un usuario desde subcollection
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object|null>} - Información de acceso a sites
   */
  async getUserSiteAccess(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      // ✅ CORRECCIÓN: Obtener desde subcollection siteAccess con path correcto
      const siteAccessData = await this.hyperContext.get(`users[${userId}].siteAccess`);

      if (!siteAccessData || Object.keys(siteAccessData).length === 0) {
        // Retornar estructura por defecto
        return {
          availableSites: [],
          defaultSite: null,
          recentSites: [],
          crossSiteSettings: {
            allowAutoSwitch: true,
            rememberLastSite: true,
            showAllSitesInMenu: true
          },
          lastSyncAt: new Date().toISOString(),
          syncVersion: 1
        };
      }

      // Convertir subcollection a estructura esperada
      const siteAccessArray = Array.isArray(siteAccessData) ? siteAccessData : Object.values(siteAccessData);
      const mainAccess = siteAccessArray.find(access => access.type === 'main') || {};

      return {
        availableSites: mainAccess.availableSites || [],
        defaultSite: mainAccess.defaultSite || null,
        recentSites: mainAccess.recentSites || [],
        crossSiteSettings: mainAccess.crossSiteSettings || {
          allowAutoSwitch: true,
          showAllSitesInMenu: true
        },
        lastSyncAt: mainAccess.lastSyncAt || new Date().toISOString(),
        syncVersion: mainAccess.syncVersion || 1
      };
    } catch (error) {
      console.error(`Error obteniendo acceso a sites para ${userId}:`, error);
      return null;
    }
  }

  /**
   * ✅ REFACTORIZACIÓN: Actualizar acceso a sites para un usuario usando subcollection
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {string} action - Acción: 'add', 'remove', 'visit'
   * @returns {Promise<Object>} - Acceso actualizado
   */
  async updateUserSiteAccess(userId, siteId, action = 'visit') {
    if (!userId || !siteId) {
      throw new Error("userId y siteId son requeridos");
    }

    try {
      // Obtener acceso actual desde subcollection
      const currentAccess = await this.getUserSiteAccess(userId);

      // Crear estructura de acceso actualizada
      const updatedAccess = {
        type: 'main',
        availableSites: currentAccess.availableSites || [],
        defaultSite: currentAccess.defaultSite || null,
        recentSites: currentAccess.recentSites || [],
        crossSiteSettings: currentAccess.crossSiteSettings || {
          allowAutoSwitch: true,
            rememberLastSite: true,
          showAllSitesInMenu: true
        },
        lastSyncAt: new Date().toISOString(),
        syncVersion: (currentAccess.syncVersion || 0) + 1
      };

      switch (action) {
        case 'add':
          if (!updatedAccess.availableSites.includes(siteId)) {
            updatedAccess.availableSites.push(siteId);
          }
          break;

        case 'remove':
          updatedAccess.availableSites = updatedAccess.availableSites.filter(
            id => id !== siteId
          );
          updatedAccess.recentSites = updatedAccess.recentSites.filter(
            site => site.siteId !== siteId
          );
          if (updatedAccess.defaultSite === siteId) {
            updatedAccess.defaultSite = updatedAccess.availableSites[0] || null;
          }
          break;

        case 'visit':
          // Actualizar historial de visitas
          const existingIndex = updatedAccess.recentSites.findIndex(
            site => site.siteId === siteId
          );

          if (existingIndex >= 0) {
            updatedAccess.recentSites[existingIndex].lastVisited = new Date().toISOString();
            updatedAccess.recentSites[existingIndex].visitCount += 1;
          } else {
            updatedAccess.recentSites.push({
              siteId: siteId,
              lastVisited: new Date().toISOString(),
              visitCount: 1
            });
          }

          // Mantener solo los 10 más recientes
          updatedAccess.recentSites.sort((a, b) =>
            new Date(b.lastVisited) - new Date(a.lastVisited)
          );
          updatedAccess.recentSites = updatedAccess.recentSites.slice(0, 10);
          break;
      }

      // ✅ CORRECCIÓN: Guardar en subcollection con path correcto
      await this.hyperContext.set(`users[${userId}].siteAccess[main]`, updatedAccess);

      // Retornar estructura simplificada para compatibilidad
      return {
        availableSites: updatedAccess.availableSites,
        defaultSite: updatedAccess.defaultSite,
        recentSites: updatedAccess.recentSites,
        crossSiteSettings: updatedAccess.crossSiteSettings,
        lastSyncAt: updatedAccess.lastSyncAt,
        syncVersion: updatedAccess.syncVersion
      };
    } catch (error) {
      console.error(`Error actualizando acceso a sites para ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Obtener perfil de usuario en un site
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @returns {Promise<Object|null>} - Perfil del site o null
   */
  async getSiteProfile(userId, siteId) {
    if (!userId || !siteId) {
      throw new Error("userId y siteId son requeridos");
    }

    try {
      // ✅ CORRECCIÓN: Obtener desde sites[siteId].profiles[userId]
      const siteProfile = await this.hyperContext.get(`sites[${siteId}].profiles[${userId}]`);
      return siteProfile;
    } catch (error) {
      console.error(`Error obteniendo perfil de site ${userId}:${siteId}:`, error);
      return null;
    }
  }

  /**
   * Obtener credencial de usuario en un site
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @returns {Promise<Object|null>} - Credencial del site o null
   */
  async getSiteCredential(userId, siteId, credentialId = null) {
    if (!userId || !siteId) {
      throw new Error("userId y siteId son requeridos");
    }

    try {
      if (credentialId) {
        // ✅ CORRECCIÓN: Obtener credencial específica desde sites[siteId].credentials[credentialId]
        const credential = await this.hyperContext.get(`sites[${siteId}].credentials[${credentialId}]`);

        // Verificar que pertenece al usuario correcto
        if (credential && credential.userId === userId) {
          return credential;
        }
        return null;
      } else {
        // ✅ CORRECCIÓN: Buscar credencial activa del usuario en el site
        // Obtener todas las credenciales del site usando subcollection
        const allCredentials = await this.hyperContext.get(`sites[${siteId}].credentials[]`);

        if (!allCredentials || allCredentials.length === 0) {
          return null;
        }

        // Buscar credencial activa del usuario
        const userCredentials = allCredentials.filter(cred =>
          cred.userId === userId && cred.status?.isActive === true
        );

        if (userCredentials.length === 0) {
          return null;
        }

        // Retornar la credencial primaria o la más reciente
        const primaryCredential = userCredentials.find(cred => cred.status?.isPrimary === true);
        if (primaryCredential) {
          return primaryCredential;
        }

        // Si no hay primaria, retornar la más reciente
        userCredentials.sort((a, b) => new Date(b.metadata?.createdAt || 0) - new Date(a.metadata?.createdAt || 0));
        return userCredentials[0];
      }
    } catch (error) {
      console.error(`Error obteniendo credencial de site ${userId}:${siteId}:${credentialId}:`, error);
      return null;
    }
  }

  /**
   * Obtener TODAS las credenciales de un usuario en un site
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @returns {Promise<Array>} - Array de credenciales
   */
  async getAllSiteCredentials(userId, siteId) {
    if (!userId || !siteId) {
      throw new Error("userId y siteId son requeridos");
    }

    try {
      const credentialsPath = `siteCredentials[]`;
      const credentials = await this.hyperContext.get(credentialsPath, {
        filters: {
          userId: { '==': userId },
          siteId: { '==': siteId }
        },
        options: {
          orderBy: { field: 'metadata.createdAt', direction: 'desc' }
        }
      });

      return credentials || [];
    } catch (error) {
      console.error(`Error obteniendo todas las credenciales de site ${userId}:${siteId}:`, error);
      return [];
    }
  }

  /**
   * Asignar múltiples credenciales a un usuario
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {Array} credentialsData - Array de datos de credenciales
   * @param {Object} options - Opciones adicionales
   * @returns {Promise<Array>} - Array de credenciales creadas
   */
  async assignMultipleCredentials(userId, siteId, credentialsData, options = {}) {
    if (!userId || !siteId || !Array.isArray(credentialsData)) {
      throw new Error("userId, siteId y credentialsData (array) son requeridos");
    }

    try {
      const createdCredentials = [];

      for (let i = 0; i < credentialsData.length; i++) {
        const credentialData = credentialsData[i];

        // Marcar la primera como primary si no hay primary especificado
        if (i === 0 && !options.primaryCredentialId) {
          credentialData.isPrimary = true;
        }

        const credential = await this.createSiteCredential(userId, siteId, credentialData);
        createdCredentials.push(credential);
      }

      // Actualizar profile con todas las credentialIds
      await this._updateProfileCredentialIds(userId, siteId, createdCredentials);

      console.log(`${credentialsData.length} credenciales asignadas a ${userId} en ${siteId}`);
      return createdCredentials;

    } catch (error) {
      console.error(`Error asignando múltiples credenciales a ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Remover una credencial específica
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {string} credentialId - ID de la credencial a remover
   * @returns {Promise<boolean>} - true si fue removida exitosamente
   */
  async removeCredential(userId, siteId, credentialId) {
    if (!userId || !siteId || !credentialId) {
      throw new Error("userId, siteId y credentialId son requeridos");
    }

    try {
      // Verificar que la credencial existe y pertenece al usuario
      const credential = await this.getSiteCredential(userId, siteId, credentialId);
      if (!credential) {
        throw new Error(`Credencial ${credentialId} no encontrada para ${userId}:${siteId}`);
      }

      // Marcar credencial como inactiva en lugar de eliminarla
      const updatedCredential = {
        ...credential,
        status: {
          ...credential.status,
          isActive: false,
          deactivatedAt: new Date().toISOString(),
          deactivatedBy: 'system'
        }
      };

      const credentialPath = `sites[${siteId}].credentials[${credentialId}]`;
      await this.hyperContext.set(credentialPath, updatedCredential);

      // ✅ AUDITORÍA: Registrar desactivación de credencial
      await this.addCredentialChangeEntry(siteId, credentialId, {
        changedBy: 'system',
        changes: ['credential_deactivated'],
        reason: 'credential_removal',
        previousValues: {
          isActive: credential.status.isActive,
          status: credential.status
        },
        newValues: {
          isActive: false,
          deactivatedAt: updatedCredential.status.deactivatedAt,
          deactivatedBy: updatedCredential.status.deactivatedBy
        },
        affectedPermissions: Object.keys(credential.permissions || {})
      });

      // Actualizar profile removiendo la credentialId
      await this._removeCredentialIdFromProfile(userId, siteId, credentialId);

      console.log(`Credencial ${credentialId} removida para ${userId}:${siteId}`);
      return true;

    } catch (error) {
      console.error(`Error removiendo credencial ${credentialId} para ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Actualizar prioridad de credenciales (cambiar cuál es primary)
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {string} newPrimaryCredentialId - ID de la nueva credencial primary
   * @returns {Promise<boolean>} - true si fue actualizada exitosamente
   */
  async updateCredentialPriority(userId, siteId, newPrimaryCredentialId) {
    if (!userId || !siteId || !newPrimaryCredentialId) {
      throw new Error("userId, siteId y newPrimaryCredentialId son requeridos");
    }

    try {
      // Verificar que la nueva credencial primary existe
      const newPrimaryCredential = await this.getSiteCredential(userId, siteId, newPrimaryCredentialId);
      if (!newPrimaryCredential) {
        throw new Error(`Credencial ${newPrimaryCredentialId} no encontrada`);
      }

      // Obtener profile actual
      const profile = await this.getSiteProfile(userId, siteId);
      if (!profile) {
        throw new Error(`Profile no encontrado para ${userId}:${siteId}`);
      }

      // Actualizar primaryCredentialId en el profile
      const updatedProfile = {
        ...profile,
        primaryCredentialId: newPrimaryCredentialId,
        metadata: {
          ...profile.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      const profilePath = `sites[${siteId}].profiles[${userId}]`;
      await this.hyperContext.set(profilePath, updatedProfile);

      // ✅ AUDITORÍA: Registrar cambio de credencial primaria
      await this.addCredentialChangeEntry(siteId, newPrimaryCredentialId, {
        changedBy: 'system',
        changes: ['credential_priority_updated'],
        reason: 'primary_credential_change',
        previousValues: {
          primaryCredentialId: profile.primaryCredentialId
        },
        newValues: {
          primaryCredentialId: newPrimaryCredentialId
        },
        affectedPermissions: Object.keys(newPrimaryCredential.permissions || {})
      });

      console.log(`Credencial primary actualizada a ${newPrimaryCredentialId} para ${userId}:${siteId}`);
      return true;

    } catch (error) {
      console.error(`Error actualizando prioridad de credencial para ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Actualizar datos privados del usuario global
   * @param {string} userId - ID del usuario
   * @param {Object} updates - Actualizaciones a aplicar
   * @returns {Promise<Object>} - Usuario actualizado
   */
  async updatePrivateData(userId, updates) {
    if (!userId || !updates) {
      throw new Error("userId y updates son requeridos");
    }

    try {
      // Obtener usuario actual
      const globalUser = await this.getGlobalUser(userId);
      if (!globalUser) {
        throw new Error(`Usuario global ${userId} no existe`);
      }

      // Aplicar actualizaciones a datos privados
      const updatedPrivateData = {
        ...globalUser.privateData,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      const updatedUser = {
        ...globalUser,
        privateData: updatedPrivateData
      };

      // ✅ CORRECCIÓN: Usar método correcto de HyperContextEnhanced (usuario ya existe, no necesita systemInitialization)
      await this.hyperContext.setGlobalEntity('users', userId, updatedUser);

      console.log(`Datos privados actualizados para usuario ${userId} - storage-based`);
      return updatedUser;

    } catch (error) {
      console.error(`Error actualizando datos privados para ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Actualizar perfil público del usuario global
   * @param {string} userId - ID del usuario
   * @param {Object} updates - Actualizaciones a aplicar
   * @returns {Promise<Object>} - Usuario actualizado
   */
  async updatePublicProfile(userId, updates) {
    if (!userId || !updates) {
      throw new Error("userId y updates son requeridos");
    }

    try {
      // Obtener usuario actual
      const globalUser = await this.getGlobalUser(userId);
      if (!globalUser) {
        throw new Error(`Usuario global ${userId} no existe`);
      }

      // Aplicar actualizaciones a perfil público
      const updatedPublicProfile = {
        ...globalUser.publicProfile,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      const updatedUser = {
        ...globalUser,
        publicProfile: updatedPublicProfile
      };

      // ✅ CORRECCIÓN: Usar método correcto de HyperContextEnhanced (usuario ya existe, no necesita systemInitialization)
      await this.hyperContext.setGlobalEntity('users', userId, updatedUser);

      console.log(`Perfil público actualizado para usuario ${userId}`);
      return updatedUser;

    } catch (error) {
      console.error(`Error actualizando perfil público para ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Actualizar perfil de usuario en un site
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {Object} updates - Actualizaciones a aplicar
   * @returns {Promise<Object>} - Perfil actualizado
   */
  async updateSiteProfile(userId, siteId, updates) {
    if (!userId || !siteId || !updates) {
      throw new Error("userId, siteId y updates son requeridos");
    }

    try {
      // Obtener perfil actual
      const siteProfile = await this.getSiteProfile(userId, siteId);
      if (!siteProfile) {
        throw new Error(`Perfil de site ${userId}:${siteId} no existe`);
      }

      // Aplicar actualizaciones
      const updatedProfile = {
        ...siteProfile,
        profile: {
          ...siteProfile.profile,
          ...updates.profile
        },
        siteConfig: {
          ...siteProfile.siteConfig,
          ...updates.siteConfig
        },
        metadata: {
          ...siteProfile.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      // Guardar perfil actualizado
      const profilePath = `siteProfiles[${userId}:${siteId}]`;
      await this.hyperContext.set(profilePath, updatedProfile);

      // También actualizar en el site (storage-based)
      await this.hyperContext.setSiteData(siteId, `userProfiles.${userId}`, updatedProfile);

      console.log(`Perfil de site actualizado: ${userId} en ${siteId}`);
      return updatedProfile;

    } catch (error) {
      console.error(`Error actualizando perfil de site ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Listar sites de un usuario
   * @param {string} userId - ID del usuario
   * @returns {Promise<Array>} - Lista de sites del usuario
   */
  async getUserSites(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      // Buscar perfiles de sites del usuario
      const profilesPath = `siteProfiles[]`;
      const profiles = await this.hyperContext.get(profilesPath, {
        filters: {
          userId: { '==': userId },
          'metadata.status': { '==': 'active' }
        },
        options: {
          orderBy: { field: 'metadata.createdAt', direction: 'desc' }
        }
      });

      return profiles || [];

    } catch (error) {
      console.error(`Error obteniendo sites del usuario ${userId}:`, error);
      return [];
    }
  }

  /**
   * Migrar usuario legacy al sistema moderno
   * @param {Object} legacyUserData - Datos del usuario legacy
   * @param {Object} firebaseUser - Usuario de Firebase Auth
   * @returns {Promise<Object>} - Usuario migrado
   */
  async migrateFromLegacyUser(legacyUserData, firebaseUser) {
    try {
      console.log(`Migrando usuario legacy: ${firebaseUser.uid}`);

      // Extraer datos del formato legacy
      const userData = {
        displayName: legacyUserData.userDoc?.data?.name || legacyUserData.userDoc?.data?.displayName,
        firstName: legacyUserData.userDoc?.data?.firstName,
        lastName: legacyUserData.userDoc?.data?.lastName,
        phoneNumber: legacyUserData.userDoc?.data?.phone,
        bio: legacyUserData.userDoc?.data?.bio,
        avatar: legacyUserData.userDoc?.data?.avatar
      };

      // Crear usuario global
      const globalUser = await this.createGlobalUser(userData, firebaseUser);

      // Migrar perfiles de instances a sites
      if (legacyUserData.instancesDocs && legacyUserData.instancesDocs.length > 0) {
        for (const instanceDoc of legacyUserData.instancesDocs) {
          const siteId = instanceDoc.data?.hash || instanceDoc.id;

          // Crear perfil del site
          await this.createSiteProfile(globalUser.id, siteId, {
            displayName: userData.displayName,
            role: legacyUserData.rolesDoc?.data?.nameSlug || 'user'
          });

          // Crear credencial del site
          if (legacyUserData.credentialsDocs) {
            const credential = legacyUserData.credentialsDocs.find(c =>
              c.data?.instanceId === instanceDoc.id
            );

            if (credential) {
              await this.createSiteCredential(globalUser.id, siteId, {
                roles: credential.data?.roles || [legacyUserData.rolesDoc?.data?.nameSlug || 'user'],
                permissions: legacyUserData.permissionsMap || {},
                isActive: credential.data?.isActive !== false,
                createdBy: 'migration'
              });
            }
          }
        }
      }

      console.log(`Usuario legacy migrado exitosamente: ${firebaseUser.uid}`);
      return globalUser;

    } catch (error) {
      console.error(`Error migrando usuario legacy:`, error);
      throw error;
    }
  }

  /**
   * Cache storage-based (no memory cache para invalidar)
   * @param {string} userId - ID del usuario (opcional)
   * @param {string} siteId - ID del site (opcional)
   */
  invalidateUserCache(userId = null, siteId = null) {
    // Storage-based cache (no memory cache para invalidar)
    console.log(`[UserManager] Cache storage-based - no memory cache para invalidar: ${userId}:${siteId}`);
  }

  /**
   * Actualizar credentialIds en el profile del usuario
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {Array} credentials - Array de credenciales
   * @private
   */
  async _updateProfileCredentialIds(userId, siteId, credentials) {
    try {
      const profile = await this.getSiteProfile(userId, siteId);
      if (!profile) {
        throw new Error(`Profile no encontrado para ${userId}:${siteId}`);
      }

      const credentialIds = credentials.map(cred => cred.id);
      const primaryCredential = credentials.find(cred => cred.isPrimary);

      const updatedProfile = {
        ...profile,
        credentialIds: [...(profile.credentialIds || []), ...credentialIds],
        primaryCredentialId: primaryCredential ? primaryCredential.id : (profile.primaryCredentialId || credentialIds[0]),
        metadata: {
          ...profile.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      const profilePath = `siteProfiles[${userId}:${siteId}]`;
      await this.hyperContext.set(profilePath, updatedProfile);

    } catch (error) {
      console.error(`Error updating profile credential IDs for ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * Remover credentialId del profile del usuario
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {string} credentialId - ID de la credencial a remover
   * @private
   */
  async _removeCredentialIdFromProfile(userId, siteId, credentialId) {
    try {
      const profile = await this.getSiteProfile(userId, siteId);
      if (!profile) {
        throw new Error(`Profile no encontrado para ${userId}:${siteId}`);
      }

      const updatedCredentialIds = (profile.credentialIds || []).filter(id => id !== credentialId);

      // Si removemos la primary credential, asignar otra como primary
      let newPrimaryCredentialId = profile.primaryCredentialId;
      if (profile.primaryCredentialId === credentialId) {
        newPrimaryCredentialId = updatedCredentialIds.length > 0 ? updatedCredentialIds[0] : null;
      }

      const updatedProfile = {
        ...profile,
        credentialIds: updatedCredentialIds,
        primaryCredentialId: newPrimaryCredentialId,
        metadata: {
          ...profile.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      const profilePath = `siteProfiles[${userId}:${siteId}]`;
      await this.hyperContext.set(profilePath, updatedProfile);

    } catch (error) {
      console.error(`Error removing credential ID from profile for ${userId}:${siteId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ OPTIMIZACIÓN: Agregar entrada de login history usando subcollection
   * @param {string} userId - ID del usuario
   * @param {Object} loginData - Datos del login
   * @returns {Promise<void>}
   */
  async addLoginHistoryEntry(userId, loginData) {
    if (!userId || !loginData) {
      throw new Error("userId y loginData son requeridos");
    }

    try {
      const loginId = `login_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // ✅ CORRECCIÓN: Agregar a subcollection loginHistory con path correcto
      await this.hyperContext.set(`users[${userId}].loginHistory[${loginId}]`, {
        id: loginId,
        timestamp: loginData.timestamp || new Date().toISOString(),
        ip: loginData.ip,
        userAgent: loginData.userAgent,
        location: loginData.location,
        success: loginData.success,
        sessionDuration: loginData.sessionDuration || null,
        deviceInfo: loginData.deviceInfo || {},
        siteId: loginData.siteId || 'main'
      });

      // Actualizar summary en documento principal
      const globalUser = await this.getGlobalUser(userId);
      if (globalUser) {
        if (!globalUser.privateData) {
          globalUser.privateData = {};
        }
        if (!globalUser.privateData.security) {
          globalUser.privateData.security = {};
        }
        if (!globalUser.privateData.security.loginSummary) {
          globalUser.privateData.security.loginSummary = {
            totalLogins: 0,
            lastLogin: null,
            recentFailures: 0,
            lastFailure: null,
            averageSessionDuration: 0
          };
        }

        const summary = globalUser.privateData.security.loginSummary;
        summary.totalLogins += 1;
        summary.lastLogin = loginData.timestamp || new Date().toISOString();

        if (!loginData.success) {
          summary.recentFailures += 1;
          summary.lastFailure = loginData.timestamp || new Date().toISOString();
        } else {
          summary.recentFailures = 0; // Reset en login exitoso
        }

        await this.hyperContext.setGlobalEntity('users', userId, globalUser);
      }

      console.log(`Login history entry added for user ${userId}`);
    } catch (error) {
      console.error(`Error agregando entrada de login history para ${userId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ OPTIMIZACIÓN: Obtener historial de login con paginación
   * @param {string} userId - ID del usuario
   * @param {number} limit - Límite de resultados
   * @param {string} startAfter - Cursor para paginación
   * @returns {Promise<Array>} - Historial de logins
   */
  async getLoginHistory(userId, limit = 20, startAfter = null) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      // ✅ CORRECCIÓN: Obtener desde subcollection con path correcto
      const historyPath = `users[${userId}].loginHistory`;
      const query = {
        orderBy: { field: 'timestamp', direction: 'desc' },
        limit: limit
      };

      if (startAfter) {
        query.startAfter = startAfter;
      }

      const loginHistory = await this.hyperContext.get(historyPath, { query });
      return Array.isArray(loginHistory) ? loginHistory : Object.values(loginHistory || {});
    } catch (error) {
      console.error(`Error obteniendo login history para ${userId}:`, error);
      return [];
    }
  }

  /**
   * ✅ OPTIMIZACIÓN: Agregar entrada de cambio en credencial usando subcollection
   * @param {string} siteId - ID del site
   * @param {string} credentialId - ID de la credencial
   * @param {Object} changeData - Datos del cambio
   * @returns {Promise<void>}
   */
  async addCredentialChangeEntry(siteId, credentialId, changeData) {
    if (!siteId || !credentialId || !changeData) {
      throw new Error("siteId, credentialId y changeData son requeridos");
    }

    try {
      const changeId = `change_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // ✅ CORRECCIÓN CRÍTICA: Usar punto (.) en lugar de barra (/) para subcollections en HyperContext
      // Agregar a subcollection changeHistory
      await this.hyperContext.set(
        `sites[${siteId}].credentials[${credentialId}].changeHistory[${changeId}]`,
        {
          id: changeId,
          timestamp: changeData.timestamp || new Date().toISOString(),
          changedBy: changeData.changedBy,
          changes: changeData.changes || [],
          reason: changeData.reason || '',
          previousValues: changeData.previousValues || {},
          newValues: changeData.newValues || {},
          affectedPermissions: changeData.affectedPermissions || []
        }
      );

      // Actualizar summary en documento principal
      const credential = await this.hyperContext.get(`sites[${siteId}].credentials[${credentialId}]`);
      if (credential) {
        if (!credential.metadata) {
          credential.metadata = {};
        }
        if (!credential.metadata.changeSummary) {
          credential.metadata.changeSummary = {
            totalChanges: 0,
            lastChange: null,
            lastChangedBy: null,
            version: 1
          };
        }

        const summary = credential.metadata.changeSummary;
        summary.totalChanges += 1;
        summary.lastChange = changeData.timestamp || new Date().toISOString();
        summary.lastChangedBy = changeData.changedBy;
        summary.version += 1;

        await this.hyperContext.set(`sites[${siteId}].credentials[${credentialId}]`, credential);
      }

      console.log(`Change history entry added for credential ${credentialId}`);
    } catch (error) {
      console.error(`Error agregando entrada de change history:`, error);
      throw error;
    }
  }

  /**
   * ✅ PRIVACY LAYERS: Obtener datos públicos del usuario (solo publicProfile)
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object|null>} - Solo datos públicos del usuario
   */
  async getPublicUserData(userId) {
    if (!userId) {
      throw new Error("userId es requerido");
    }

    try {
      const globalUser = await this.getGlobalUser(userId);
      if (!globalUser) {
        return null;
      }

      // Solo retornar datos públicos
      return {
        id: globalUser.id,
        publicProfile: globalUser.publicProfile || {
          displayName: 'Usuario',
          avatar: '',
          bio: '',
          website: '',
          location: '',
          timezone: 'America/Mexico_City',
          language: 'es',
          createdAt: globalUser.metadata?.createdAt || new Date().toISOString(),
          updatedAt: globalUser.metadata?.updatedAt || new Date().toISOString()
        },
        metadata: {
          accountStatus: globalUser.metadata?.accountStatus || 'active',
          lastLoginAt: globalUser.metadata?.lastLoginAt,
          isActive: globalUser.metadata?.isActive !== false
        }
      };
    } catch (error) {
      console.error(`Error obteniendo datos públicos para ${userId}:`, error);
      return null;
    }
  }

  /**
   * ✅ PRIVACY LAYERS: Obtener perfil de site para populate (datos limitados)
   * @param {string} userId - ID del usuario
   * @param {string} siteId - ID del site
   * @param {string} requesterId - ID del usuario que solicita (para validar permisos)
   * @returns {Promise<Object|null>} - Datos del perfil de site para populate
   */
  async getSiteProfileForPopulate(userId, siteId, requesterId = null) {
    if (!userId || !siteId) {
      throw new Error("userId y siteId son requeridos");
    }

    try {
      // Obtener perfil de site
      const siteProfile = await this.getSiteProfile(userId, siteId);
      if (!siteProfile) {
        return null;
      }

      // Obtener datos públicos del usuario global
      const publicData = await this.getPublicUserData(userId);
      if (!publicData) {
        return null;
      }

      // Combinar datos públicos con datos específicos del site
      return {
        userId: userId,
        siteId: siteId,

        // Datos públicos del usuario global
        displayName: publicData.publicProfile.displayName,
        avatar: publicData.publicProfile.avatar,
        bio: publicData.publicProfile.bio,

        // Datos específicos del site (limitados)
        siteProfile: {
          displayName: siteProfile.profile?.displayName || publicData.publicProfile.displayName,
          avatar: siteProfile.profile?.avatar || publicData.publicProfile.avatar,
          role: siteProfile.profile?.role || 'user',
          department: siteProfile.profile?.department || '',
          position: siteProfile.profile?.position || '',
          joinedAt: siteProfile.profile?.joinedAt,
          isActive: siteProfile.profile?.isActive !== false
        },

        // Roles agregados (sin permisos específicos)
        roles: siteProfile.aggregatedRoles || [],

        // Estado básico
        status: {
          isActive: siteProfile.status?.isActive !== false,
          isVerified: siteProfile.status?.isVerified || false,
          lastActivity: siteProfile.status?.lastActivity
        },

        // Metadatos limitados
        metadata: {
          joinedAt: siteProfile.metadata?.createdAt,
          accountStatus: publicData.metadata.accountStatus
        }
      };
    } catch (error) {
      console.error(`Error obteniendo perfil de site para populate ${userId}:${siteId}:`, error);
      return null;
    }
  }

  /**
   * ✅ PRIVACY LAYERS: Obtener datos completos para formulario de edición del propio usuario
   * @param {string} userId - ID del usuario
   * @param {string} requesterId - ID del usuario que solicita (debe ser el mismo)
   * @returns {Promise<Object|null>} - Datos completos para edición
   */
  async getCompleteUserDataForEditing(userId, requesterId) {
    if (!userId || !requesterId) {
      throw new Error("userId y requesterId son requeridos");
    }

    // Verificar que el usuario solicita sus propios datos
    if (userId !== requesterId) {
      throw new Error("Solo el propio usuario puede acceder a sus datos completos para edición");
    }

    try {
      // ✅ CORRECCIÓN: Pasar systemInitialization cuando el usuario accede a sus propios datos
      const globalUser = await this.getGlobalUser(userId, { systemInitialization: true });
      if (!globalUser) {
        // ✅ CORRECCIÓN: Log más suave para usuario no encontrado
        console.log(`🔍 Usuario ${userId} no encontrado en getCompleteUserDataForEditing, será creado automáticamente`);
        throw new Error('Usuario no encontrado');
      }

      // Retornar datos completos para edición
      return {
        id: globalUser.id,

        // Datos públicos (editables)
        publicProfile: globalUser.publicProfile || {},

        // Datos privados (editables por el usuario)
        privateData: {
          email: globalUser.privateData?.email,
          emailVerified: globalUser.privateData?.emailVerified,
          phoneNumber: globalUser.privateData?.phoneNumber,
          firstName: globalUser.privateData?.firstName,
          lastName: globalUser.privateData?.lastName,
          dateOfBirth: globalUser.privateData?.dateOfBirth,

          // Preferencias (editables)
          preferences: globalUser.privateData?.preferences || {},

          // Datos de seguridad (solo lectura/configuración)
          security: {
            twoFactorEnabled: globalUser.privateData?.security?.twoFactorEnabled || false,
            lastPasswordChange: globalUser.privateData?.security?.lastPasswordChange,
            loginSummary: globalUser.privateData?.security?.loginSummary || {}
          }
        },

        // Referencias de sites (solo lectura)
        siteAccess: globalUser.siteAccess || {},

        // Metadatos del sistema (solo lectura)
        metadata: globalUser.metadata || {}
      };
    } catch (error) {
      console.error(`Error obteniendo datos completos para edición ${userId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ PRIVACY LAYERS: Obtener datos de usuario con nivel de acceso específico
   * @param {string} userId - ID del usuario objetivo
   * @param {string} requesterId - ID del usuario que solicita
   * @param {string} siteId - ID del site (opcional)
   * @param {string} accessLevel - Nivel de acceso: 'public', 'site_member', 'admin', 'self'
   * @param {Object} permissionCalculator - Instancia del PermissionCalculator (opcional)
   * @returns {Promise<Object|null>} - Datos según nivel de acceso
   */
  async getUserDataWithAccessLevel(userId, requesterId, siteId = null, accessLevel = 'public', permissionCalculator = null) {
    if (!userId || !requesterId) {
      throw new Error("userId y requesterId son requeridos");
    }

    try {
      switch (accessLevel) {
        case 'public':
          // Solo datos públicos
          return await this.getPublicUserData(userId);

        case 'site_member':
          // Datos para populate en el contexto del site
          if (!siteId) {
            throw new Error("siteId es requerido para accessLevel 'site_member'");
          }
          return await this.getSiteProfileForPopulate(userId, siteId, requesterId);

        case 'admin':
          // Datos extendidos para administradores (sin datos sensibles)
          const publicData = await this.getPublicUserData(userId);
          const siteProfile = siteId ? await this.getSiteProfile(userId, siteId) : null;

          return {
            ...publicData,
            siteProfile: siteProfile ? {
              ...siteProfile,
              // Remover datos sensibles
              siteConfig: {
                notifications: siteProfile.siteConfig?.notifications,
                preferences: siteProfile.siteConfig?.preferences
                // No incluir customFields que pueden tener datos sensibles
              }
            } : null,
            adminMetadata: {
              totalSites: (await this.getUserSites(userId)).length,
              accountCreated: publicData.metadata?.createdAt,
              lastActivity: publicData.metadata?.lastLoginAt
            }
          };

        case 'self':
          // Datos completos para el propio usuario incluyendo credencial del site
          let completeUserData;
          try {
            completeUserData = await this.getCompleteUserDataForEditing(userId, requesterId);
          } catch (error) {
            // ✅ CORRECCIÓN: Manejo suave de usuario no encontrado
            if (error.message === 'Usuario no encontrado') {
              console.log(`🔍 Usuario ${userId} no encontrado, será creado automáticamente por el sistema`);
              // El sistema creará el usuario automáticamente, reintentamos
              throw error; // Re-lanzar para que el sistema maneje la creación
            }
            throw error; // Re-lanzar otros errores
          }

          // ✅ CORRECCIÓN: Incluir credencial del site si se especifica siteId
          if (siteId) {
            const siteCredential = await this.getSiteCredential(userId, siteId);
            const siteProfile = await this.getSiteProfile(userId, siteId);

            completeUserData.siteCredential = siteCredential;
            completeUserData.siteProfile = siteProfile;
            completeUserData.effectivePermissions = (siteCredential && permissionCalculator) ?
              await permissionCalculator.calculateEffectivePermissions(userId, siteId) : {};
          }

          return completeUserData;

        default:
          throw new Error(`Nivel de acceso no válido: ${accessLevel}`);
      }
    } catch (error) {
      console.error(`Error obteniendo datos con nivel de acceso ${accessLevel} para ${userId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ CLEANUP: Inicializar CleanupManager para datos históricos
   * @returns {Promise<CleanupManager>} - Instancia del CleanupManager
   */
  async initializeCleanupManager() {
    try {
      const { CleanupManager } = await import('./CleanupManager.js');

      const cleanupConfig = {
        // TTL: 90 días para loginHistory, 1 año para changeHistory
        loginHistoryTTL: 90 * 24 * 60 * 60 * 1000,
        changeHistoryTTL: 365 * 24 * 60 * 60 * 1000,

        // Límites de documentos
        maxLoginHistoryEntries: 1000,
        maxChangeHistoryEntries: 500,

        // Configuración de ejecución
        enableAutoCleanup: true,
        cleanupInterval: 24 * 60 * 60 * 1000, // 24 horas

        // Logging
        enableLogging: true,
        logLevel: 'info'
      };

      const cleanupManager = new CleanupManager(this.hyperContext, cleanupConfig);

      // Programar cleanup automático
      cleanupManager.scheduleAutoCleanup();

      console.log('✅ CleanupManager inicializado con cleanup automático');
      return cleanupManager;

    } catch (error) {
      console.error('❌ Error inicializando CleanupManager:', error);
      throw error;
    }
  }

  /**
   * ✅ CLEANUP: Ejecutar cleanup manual de datos históricos
   * @returns {Promise<Object>} - Estadísticas del cleanup
   */
  async runHistoricalDataCleanup() {
    try {
      const cleanupManager = await this.initializeCleanupManager();
      const stats = await cleanupManager.runFullCleanup();

      console.log('✅ Cleanup manual completado:', stats);
      return {
        success: true,
        stats: stats
      };

    } catch (error) {
      console.error('❌ Error en cleanup manual:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * ✅ AUDITORÍA: Actualizar credencial con auditoría automática
   * @param {string} siteId - ID del site
   * @param {string} credentialId - ID de la credencial
   * @param {Object} updates - Actualizaciones a aplicar
   * @param {Object} auditData - Datos de auditoría
   * @returns {Promise<Object>} - Credencial actualizada
   */
  async updateCredentialWithAudit(siteId, credentialId, updates, auditData) {
    if (!siteId || !credentialId || !updates || !auditData) {
      throw new Error("siteId, credentialId, updates y auditData son requeridos");
    }

    try {
      // Obtener credencial actual
      const currentCredential = await this.hyperContext.get(`sites[${siteId}].credentials[${credentialId}]`);
      if (!currentCredential) {
        throw new Error(`Credencial ${credentialId} no encontrada`);
      }

      // Aplicar actualizaciones
      const updatedCredential = {
        ...currentCredential,
        ...updates,
        metadata: {
          ...currentCredential.metadata,
          updatedAt: new Date().toISOString(),
          version: (currentCredential.metadata?.version || 1) + 1
        }
      };

      // Guardar credencial actualizada
      await this.hyperContext.set(`sites[${siteId}].credentials[${credentialId}]`, updatedCredential);

      // Registrar cambios en auditoría
      const changes = [];
      const previousValues = {};
      const newValues = {};
      const affectedPermissions = [];

      // Detectar cambios automáticamente
      if (updates.roles && JSON.stringify(updates.roles) !== JSON.stringify(currentCredential.roles)) {
        changes.push('roles_updated');
        previousValues.roles = currentCredential.roles;
        newValues.roles = updates.roles;
      }

      if (updates.permissions && JSON.stringify(updates.permissions) !== JSON.stringify(currentCredential.permissions)) {
        changes.push('permissions_updated');
        previousValues.permissions = currentCredential.permissions;
        newValues.permissions = updates.permissions;
        affectedPermissions.push(...Object.keys(updates.permissions || {}));
      }

      if (updates.status && JSON.stringify(updates.status) !== JSON.stringify(currentCredential.status)) {
        changes.push('status_updated');
        previousValues.status = currentCredential.status;
        newValues.status = updates.status;
      }

      // Registrar auditoría
      await this.addCredentialChangeEntry(siteId, credentialId, {
        changedBy: auditData.changedBy || 'system',
        changes: changes.length > 0 ? changes : ['credential_updated'],
        reason: auditData.reason || 'credential_modification',
        previousValues,
        newValues,
        affectedPermissions
      });

      console.log(`Credencial ${credentialId} actualizada con auditoría`);
      return updatedCredential;

    } catch (error) {
      console.error(`Error actualizando credencial con auditoría ${credentialId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ AUDITORÍA: Obtener historial de cambios de una credencial
   * @param {string} siteId - ID del site
   * @param {string} credentialId - ID de la credencial
   * @param {number} limit - Límite de resultados
   * @returns {Promise<Array>} - Historial de cambios
   */
  async getCredentialChangeHistory(siteId, credentialId, limit = 50) {
    if (!siteId || !credentialId) {
      throw new Error("siteId y credentialId son requeridos");
    }

    try {
      // ✅ CORRECCIÓN CRÍTICA: Usar punto (.) en lugar de barra (/) para subcollections en HyperContext
      const historyPath = `sites[${siteId}].credentials[${credentialId}].changeHistory`;
      const query = {
        orderBy: { field: 'timestamp', direction: 'desc' },
        limit: limit
      };

      const changeHistory = await this.hyperContext.get(historyPath, { query });
      return Array.isArray(changeHistory) ? changeHistory : Object.values(changeHistory || {});
    } catch (error) {
      console.error(`Error obteniendo historial de cambios para credencial ${credentialId}:`, error);
      return [];
    }
  }

  /**
   * ✅ AUDITORÍA: Obtener estadísticas de auditoría para un site
   * @param {string} siteId - ID del site
   * @returns {Promise<Object>} - Estadísticas de auditoría
   */
  async getAuditStatistics(siteId) {
    if (!siteId) {
      throw new Error("siteId es requerido");
    }

    try {
      const stats = {
        totalCredentials: 0,
        totalChanges: 0,
        recentChanges: 0, // Últimas 24 horas
        changesByType: {},
        changesByUser: {},
        lastChange: null
      };

      // Obtener todas las credenciales del site
      const credentials = await this.hyperContext.get(`sites[${siteId}].credentials`);
      if (!credentials) {
        return stats;
      }

      stats.totalCredentials = Object.keys(credentials).length;
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      // Analizar cada credencial
      for (const [credentialId, credential] of Object.entries(credentials)) {
        if (credential.metadata?.changeSummary) {
          stats.totalChanges += credential.metadata.changeSummary.totalChanges || 0;

          if (credential.metadata.changeSummary.lastChange) {
            const lastChangeDate = new Date(credential.metadata.changeSummary.lastChange);
            if (!stats.lastChange || lastChangeDate > new Date(stats.lastChange)) {
              stats.lastChange = credential.metadata.changeSummary.lastChange;
            }

            if (lastChangeDate > oneDayAgo) {
              stats.recentChanges += 1;
            }
          }
        }

        // Obtener historial detallado para estadísticas
        try {
          const history = await this.getCredentialChangeHistory(siteId, credentialId, 10);

          history.forEach(change => {
            // Contar por tipo de cambio
            change.changes?.forEach(changeType => {
              stats.changesByType[changeType] = (stats.changesByType[changeType] || 0) + 1;
            });

            // Contar por usuario
            if (change.changedBy) {
              stats.changesByUser[change.changedBy] = (stats.changesByUser[change.changedBy] || 0) + 1;
            }
          });
        } catch (historyError) {
          console.warn(`Error obteniendo historial para credencial ${credentialId}:`, historyError);
        }
      }

      return stats;
    } catch (error) {
      console.error(`Error obteniendo estadísticas de auditoría para site ${siteId}:`, error);
      return {
        totalCredentials: 0,
        totalChanges: 0,
        recentChanges: 0,
        changesByType: {},
        changesByUser: {},
        lastChange: null,
        error: error.message
      };
    }
  }

  /**
   * ✅ PERFORMANCE: Inicializar PerformanceMonitor
   * @returns {Promise<void>}
   */
  async initializePerformanceMonitor() {
    try {
      const { PerformanceMonitor } = await import('./PerformanceMonitor.js');

      const performanceConfig = {
        enableMetrics: true,
        enableDetailedLogging: false,
        metricsRetentionDays: 30,
        aggregationInterval: 60 * 60 * 1000, // 1 hora
        slowQueryThreshold: 1000, // 1 segundo
        slowLoginThreshold: 2000, // 2 segundos
        lowCacheHitThreshold: 0.7, // 70%
        enableAlerts: true,
        alertThresholds: {
          errorRate: 0.05, // 5%
          avgResponseTime: 3000, // 3 segundos
          cacheHitRate: 0.6 // 60%
        }
      };

      this.performanceMonitor = new PerformanceMonitor(this.hyperContext, performanceConfig);
      console.log('✅ PerformanceMonitor inicializado');

    } catch (error) {
      console.error('❌ Error inicializando PerformanceMonitor:', error);
      // No fallar la inicialización por error de performance monitor
    }
  }

  /**
   * ✅ PERFORMANCE: Wrapper para medir performance de funciones
   * @param {string} functionName - Nombre de la función
   * @param {Function} fn - Función a ejecutar
   * @param {Object} context - Contexto adicional
   * @returns {Promise<any>} - Resultado de la función
   */
  async measurePerformance(functionName, fn, context = {}) {
    const startTime = Date.now();
    let success = true;
    let error = null;

    try {
      const result = await fn();
      return result;
    } catch (err) {
      success = false;
      error = err;

      // Registrar error en métricas
      if (this.performanceMonitor) {
        this.performanceMonitor.recordError({
          type: err.name || 'UnknownError',
          function: functionName,
          message: err.message,
          context
        });
      }

      throw err;
    } finally {
      const duration = Date.now() - startTime;

      // Registrar métrica de query
      if (this.performanceMonitor) {
        this.performanceMonitor.recordQuery({
          type: 'user_management',
          path: functionName,
          duration,
          success,
          context
        });
      }
    }
  }

  /**
   * ✅ PERFORMANCE: Registrar login con métricas
   * @param {Object} loginData - Datos del login
   * @returns {Promise<void>}
   */
  async recordLoginMetrics(loginData) {
    if (this.performanceMonitor) {
      this.performanceMonitor.recordLogin(loginData);
    }
  }

  /**
   * ✅ PERFORMANCE: Registrar cache hit/miss
   * @param {Object} cacheData - Datos del cache
   * @returns {Promise<void>}
   */
  async recordCacheMetrics(cacheData) {
    if (this.performanceMonitor) {
      this.performanceMonitor.recordCache(cacheData);
    }
  }

  /**
   * ✅ PERFORMANCE: Obtener métricas actuales
   * @returns {Object} - Métricas de performance
   */
  getPerformanceMetrics() {
    if (this.performanceMonitor) {
      return this.performanceMonitor.getCurrentMetrics();
    }
    return null;
  }

  /**
   * ✅ PERFORMANCE: Obtener dashboard de métricas
   * @returns {Object} - Dashboard de performance
   */
  getPerformanceDashboard() {
    if (this.performanceMonitor) {
      return this.performanceMonitor.getDashboard();
    }
    return null;
  }

  /**
   * ✅ PERFORMANCE: Actualizar métricas de usuarios
   * @returns {Promise<void>}
   */
  async updateUserMetricsForMonitoring() {
    try {
      if (!this.performanceMonitor) return;

      // Obtener estadísticas de usuarios activos
      const activeUsers = await this.getActiveUsersCount();
      const newUsers = await this.getNewUsersCount();
      const usersByRole = await this.getUsersByRole();
      const usersBySite = await this.getUsersBySite();

      this.performanceMonitor.updateUserMetrics({
        activeUsers,
        newUsers,
        usersByRole,
        usersBySite
      });

    } catch (error) {
      console.error('Error actualizando métricas de usuarios:', error);
    }
  }

  /**
   * ✅ PERFORMANCE: Obtener conteo de usuarios activos
   * @returns {Promise<number>} - Número de usuarios activos
   */
  async getActiveUsersCount() {
    try {
      // Implementación simplificada - en producción usar queries más eficientes
      const users = await this.hyperContext.get('users');
      if (!users) return 0;

      return Object.values(users).filter(user =>
        user.metadata?.isActive !== false
      ).length;
    } catch (error) {
      console.error('Error obteniendo conteo de usuarios activos:', error);
      return 0;
    }
  }

  /**
   * ✅ PERFORMANCE: Obtener conteo de usuarios nuevos (últimas 24h)
   * @returns {Promise<number>} - Número de usuarios nuevos
   */
  async getNewUsersCount() {
    try {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const users = await this.hyperContext.get('users');
      if (!users) return 0;

      return Object.values(users).filter(user => {
        const createdAt = new Date(user.metadata?.createdAt || 0);
        return createdAt > oneDayAgo;
      }).length;
    } catch (error) {
      console.error('Error obteniendo conteo de usuarios nuevos:', error);
      return 0;
    }
  }

  /**
   * ✅ PERFORMANCE: Obtener usuarios por rol
   * @returns {Promise<Object>} - Usuarios agrupados por rol
   */
  async getUsersByRole() {
    try {
      const usersByRole = {};
      const sites = await this.hyperContext.get('sites');

      if (!sites) return usersByRole;

      for (const [siteId, site] of Object.entries(sites)) {
        if (site.profiles) {
          for (const profile of Object.values(site.profiles)) {
            const roles = profile.aggregatedRoles || ['user'];
            roles.forEach(role => {
              usersByRole[role] = (usersByRole[role] || 0) + 1;
            });
          }
        }
      }

      return usersByRole;
    } catch (error) {
      console.error('Error obteniendo usuarios por rol:', error);
      return {};
    }
  }

  /**
   * ✅ PERFORMANCE: Obtener usuarios por site
   * @returns {Promise<Object>} - Usuarios agrupados por site
   */
  async getUsersBySite() {
    try {
      const usersBySite = {};
      const sites = await this.hyperContext.get('sites');

      if (!sites) return usersBySite;

      for (const [siteId, site] of Object.entries(sites)) {
        if (site.profiles) {
          usersBySite[siteId] = Object.keys(site.profiles).length;
        }
      }

      return usersBySite;
    } catch (error) {
      console.error('Error obteniendo usuarios por site:', error);
      return {};
    }
  }
}
