/**
 * Proyector v1.0 Handler Unificado - Endpoint único para todo el Proyector
 * 
 * ARQUITECTURA FINAL:
 * - 1 onCall: proyector_v1 (Cloud Function)
 * - 1 onRequest: proyector_v1_http (HTTP con prefijo /v1/)
 * - Routing dinámico interno para toda la funcionalidad
 * - Compatibilidad 100% con frontend actual
 * 
 * ROUTING INTERNO:
 * - Core: kagSolver, aiTools, fileProcessor, botAgents, cache, permissions
 * - Sites: codeblocks, webhooks, api, config, management, dataAccess
 * - Modules: sales, cart, contacts, pages, management, cross-module
 * - CodeBlocks: execute, load, validate, monitor, assets, flows
 * - Firebase: analytics, resourceTracking, indexManagement, domainManagement
 */

import { onCall, onRequest } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';

// Importar handlers existentes para reutilizar la lógica
import { proyectorUnifiedHandler } from '../UnifiedHandler.js';
import { sitesUnifiedHandler } from '../SitesUnifiedHandler.js';
import { modulesUnifiedHandler } from '../ModulesUnifiedHandler.js';
import { codeBlocksUnifiedHandler } from '../CodeBlocksUnifiedHandler.js';
import { SystemInitializer } from '../SystemInitializer.js';
import { endpointRegistry } from '../config/EndpointRegistry.js';

// Configuración para el handler unificado
const handlerConfig = {
  cors: {
    origin: process.env.PROYECTOR_FUNCTIONS_EMULATOR === 'true'
      ? ['*', 'http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:5001', 'http://localhost:2230', 'http://localhost:3330']
      : (process.env.PROYECTOR_CORS_ALLOWED_ORIGINS_PRODUCTION
          ? process.env.PROYECTOR_CORS_ALLOWED_ORIGINS_PRODUCTION.split(',').map(origin => origin.trim())
          : ['https://tranqui-pro.web.app', 'https://tranqui-pro.firebaseapp.com', 'https://tranqui.pro', 'https://www.tranqui.pro']),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Content-Length', 'X-Requested-With', 'Accept', 'Origin'],
    credentials: true,
    maxAge: 3600
  },
  // Configuración estándar de Cloud Functions
  minInstances: 0,
  maxInstances: 10,
  timeoutSeconds: 300,
  memory: '512MiB',
  cpu: 1,
  region: 'us-central1'
};

/**
 * Proyector v1.0 Cloud Function (onCall)
 * Endpoint principal para todas las operaciones del Proyector
 */
export const proyectorV1Handler = onCall(handlerConfig, async (request) => {
  try {
    // Inicializar endpoint registry si no está inicializado
    if (!endpointRegistry.initialized) {
      await endpointRegistry.initialize();
    }

    const { category, component, action, data, siteId } = request.data;

    // Validar parámetros requeridos
    if (!category) {
      throw new Error('Category parameter is required (core|sites|modules|codeblocks|firebase|ai|tools|system)');
    }

    // Crear contexto de ejecución unificado
    const executionContext = {
      userId: request.auth?.uid || 'anonymous',
      siteId: siteId || 'default',
      timestamp: Date.now(),
      requestId: generateRequestId(),
      category,
      component,
      action
    };

    // Registrar llamada al endpoint para estadísticas
    const routingKey = action ? `${category}.${component}.${action}` : `${category}.${component}`;
    endpointRegistry.recordEndpointCall(routingKey);

    logger.info(`Proyector v1.0: ${routingKey} for site ${executionContext.siteId}`);

    // Routing dinámico por categoría
    let result;
    switch (category) {
      case 'system':
        // Manejar inicialización del sistema
        result = await handleSystemOperations(component, action, data, executionContext);
        break;

      case 'core':
        // Delegar al proyectorUnifiedHandler
        result = await delegateToHandler(proyectorUnifiedHandler, {
          component,
          action,
          data,
          siteId
        }, request.auth);
        break;

      case 'sites':
        // Delegar al sitesUnifiedHandler
        result = await delegateToHandler(sitesUnifiedHandler, {
          siteId,
          component,
          action,
          data
        }, request.auth);
        break;

      case 'modules':
        // Delegar al modulesUnifiedHandler
        result = await delegateToHandler(modulesUnifiedHandler, {
          moduleId: component,
          action,
          data,
          siteId
        }, request.auth);
        break;

      case 'codeblocks':
        // Delegar al codeBlocksUnifiedHandler
        result = await delegateToHandler(codeBlocksUnifiedHandler, {
          siteId,
          codeBlockId: component,
          action,
          data
        }, request.auth);
        break;

      case 'firebase':
        // Firebase integration integrado en core
        result = await delegateToHandler(proyectorUnifiedHandler, {
          component: 'firebaseIntegration',
          action: `${component}.${action}`,
          data,
          siteId
        }, request.auth);
        break;

      case 'ai':
        // Manejar operaciones de AI (consolidadas desde funciones dispersas)
        result = await handleAIOperations(component, action, data, executionContext, request.auth);
        break;

      case 'tools':
        // Manejar operaciones de Tools (consolidadas desde funciones dispersas)
        result = await handleToolsOperations(component, action, data, executionContext, request.auth);
        break;

      case 'triggers':
        // Manejar operaciones de Triggers (consolidadas desde triggers cross-module)
        result = await handleTriggersOperations(component, action, data, executionContext);
        break;

      default:
        throw new Error(`Category not found: ${category}`);
    }

    return {
      success: true,
      category,
      component,
      action,
      result,
      executionContext: {
        requestId: executionContext.requestId,
        timestamp: executionContext.timestamp,
        executionTime: Date.now() - executionContext.timestamp
      }
    };

  } catch (error) {
    logger.error('Error in Proyector v1.0 Handler:', error);
    
    return {
      success: false,
      error: error.message,
      category: request.data?.category,
      component: request.data?.component,
      action: request.data?.action,
      timestamp: Date.now()
    };
  }
});

/**
 * Proyector v1.0 HTTP Handler (onRequest)
 * Endpoint HTTP con prefijo /v1/ para requests HTTP
 */
export const proyectorV1HttpHandler = onRequest(handlerConfig, async (req, res) => {
  try {
    // Parsear la URL para extraer el routing
    const urlPath = req.path.replace('/v1/', '');
    const pathParts = urlPath.split('/');
    
    if (pathParts.length < 3) {
      throw new Error('Invalid URL format. Expected: /v1/{category}/{component}/{action}');
    }

    const [category, component, action] = pathParts;
    const data = req.method === 'GET' ? req.query : req.body;
    const siteId = req.headers['x-site-id'] || req.query.siteId || 'default';

    // Crear request simulado para reutilizar la lógica del onCall
    const simulatedRequest = {
      data: {
        category,
        component,
        action,
        data,
        siteId
      },
      auth: req.headers.authorization ? { uid: 'http-user' } : null
    };

    // Reutilizar la lógica del handler onCall
    const result = await proyectorV1Handler.run(simulatedRequest);

    // Responder con formato HTTP
    res.status(200).json(result);

  } catch (error) {
    logger.error('Error in Proyector v1.0 HTTP Handler:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: Date.now()
    });
  }
});

/**
 * Función auxiliar para delegar a handlers existentes
 */
async function delegateToHandler(handler, requestData, auth) {
  const simulatedRequest = {
    data: requestData,
    auth
  };

  // Ejecutar el handler y extraer el resultado
  const response = await handler.run(simulatedRequest);
  
  if (response.success) {
    return response.result;
  } else {
    throw new Error(response.error);
  }
}

/**
 * Manejar operaciones del sistema (inicialización, estado, etc.)
 */
async function handleSystemOperations(component, action, data, executionContext) {
  const systemInitializer = new SystemInitializer();

  switch (component) {
    case 'initialization':
      switch (action) {
        case 'getSystemStatus':
        case 'checkStatus':
          // ✅ CORRECCIÓN: Estructura de respuesta consistente
          await systemInitializer.initialize();
          const systemStatus = await systemInitializer.getSystemStatus();
          return {
            success: true,
            data: systemStatus
          };

        case 'initializeSystem':
          // ✅ CORRECCIÓN: Forzar inicialización del sistema con userId
          await systemInitializer.initialize();

          // Si se proporciona userId, asignar como primer usuario automáticamente
          if (data.userId) {
            console.log('👑 Asignando usuario automáticamente durante inicialización:', data.userId);
            await systemInitializer.assignFirstUserAsSuperAdmin(data.userId);
          }

          const statusAfterInit = await systemInitializer.getSystemStatus();
          return {
            success: true,
            data: {
              message: 'Sistema inicializado correctamente',
              status: statusAfterInit,
              userAssigned: !!data.userId
            }
          };

        case 'assignFirstUser':
          // Asignar primer usuario como super admin
          if (!data.userId) {
            throw new Error('userId is required for assignFirstUser action');
          }
          await systemInitializer.initialize();
          const assignResult = await systemInitializer.assignFirstUserAsSuperAdmin(data.userId, data.firebaseUser);
          const statusAfterAssign = await systemInitializer.getSystemStatus();
          return {
            success: true,
            data: {
              message: 'Primer usuario asignado como super admin',
              userId: data.userId,
              result: assignResult,
              status: statusAfterAssign
            }
          };

        case 'createUserSession':
          // Crear sesión completa para usuario (perfil global + credencial + ownership si es primer user)
          return await handleCreateUserSession(data, executionContext);

        default:
          throw new Error(`Unknown initialization action: ${action}`);
      }

    case 'webhooks':
      // Manejar operaciones de webhooks usando CodeBlock system-webhooks
      return await handleWebhooksOperations(action, data, executionContext);

    case 'tunnel':
      // Manejar operaciones de tunnel usando CodeBlock system-webhooks
      return await handleTunnelOperations(action, data, executionContext);

    default:
      throw new Error(`Unknown system component: ${component}. Valid components: initialization, webhooks, tunnel`);
  }
}

/**
 * Manejar operaciones de webhooks usando backend WebhooksController
 */
async function handleWebhooksOperations(action, data, executionContext) {
  try {
    // ✅ CORRECCIÓN: Usar WebhooksController del backend en lugar de importar desde public/
    const { WebhooksController } = await import('../WebhookManager.js');

    const siteId = data.siteId || executionContext.siteId || 'main';
    const controller = new WebhooksController(siteId);

    switch (action) {
      case 'telegram':
        const operation = data.operation || 'setup';

        switch (operation) {
          case 'setup':
            if (!data.tunnelUrl) {
              throw new Error('tunnelUrl is required for webhook setup');
            }
            return await controller.setupTelegramWebhooks(data.tunnelUrl, data.options || {});

          case 'status':
            return await controller.getStatus();

          case 'cleanup':
            return await controller.cleanup();

          default:
            throw new Error(`Unknown webhook operation: ${operation}`);
        }

      default:
        throw new Error(`Unknown webhook action: ${action}`);
    }

  } catch (error) {
    logger.error('Error in webhook operations:', error);
    return {
      success: false,
      error: error.message,
      action,
      siteId: executionContext.siteId
    };
  }
}

/**
 * Manejar operaciones de tunnel usando backend WebhooksController
 */
async function handleTunnelOperations(action, data, executionContext) {
  try {
    // ✅ CORRECCIÓN: Usar WebhooksController del backend en lugar de importar desde public/
    const { WebhooksController } = await import('../WebhookManager.js');

    const siteId = data.siteId || executionContext.siteId || 'main';
    const controller = new WebhooksController(siteId);

    switch (action) {
      case 'start':
        const port = data.port || 5001;
        const autoSetup = data.autoSetup !== false; // Default true
        return await controller.startTunnelAndSetupWebhooks(port, autoSetup);

      case 'stop':
        return await controller.cleanup();

      case 'status':
        return await controller.getStatus();

      default:
        throw new Error(`Unknown tunnel action: ${action}`);
    }

  } catch (error) {
    logger.error('Error in tunnel operations:', error);
    return {
      success: false,
      error: error.message,
      action,
      siteId: executionContext.siteId
    };
  }
}

/**
 * Manejar operaciones de AI (consolidadas desde funciones dispersas)
 */
async function handleAIOperations(component, action, data, executionContext, auth) {
  switch (component) {
    case 'processor':
      // Consolidado desde proyectorAIProcessor
      const { UnifiedAIManager } = await import('../UnifiedAIManager.js');
      const aiManager = new UnifiedAIManager();
      await aiManager.initialize();

      return await aiManager.processAIRequest(
        executionContext.siteId,
        data.requestId || executionContext.requestId,
        data.requestData || data
      );

    case 'chat':
      // AI chat processing
      return await delegateToHandler(proyectorUnifiedHandler, {
        component: 'aiChat',
        action,
        data,
        siteId: executionContext.siteId
      }, auth);

    case 'memory':
      // AI memory management
      return await delegateToHandler(proyectorUnifiedHandler, {
        component: 'aiMemory',
        action,
        data,
        siteId: executionContext.siteId
      }, auth);

    default:
      throw new Error(`Unknown AI component: ${component}`);
  }
}

/**
 * Manejar operaciones de Tools (consolidadas desde funciones dispersas)
 */
async function handleToolsOperations(component, action, data, executionContext, auth) {
  switch (component) {
    case 'registry':
      if (action === 'getTools') {
        // Consolidado desde toolsGetTools
        return await delegateToHandler(proyectorUnifiedHandler, {
          component: 'tools',
          action: 'getTools',
          data,
          siteId: executionContext.siteId
        }, auth);
      }
      break;

    case 'execution':
      if (action === 'executeTool') {
        // Consolidado desde toolsExecuteTool
        return await delegateToHandler(proyectorUnifiedHandler, {
          component: 'tools',
          action: 'executeTool',
          data,
          siteId: executionContext.siteId
        }, auth);
      }
      break;

    case 'metrics':
      if (action === 'getMetrics') {
        // Consolidado desde toolsGetMetrics
        return await delegateToHandler(proyectorUnifiedHandler, {
          component: 'tools',
          action: 'getMetrics',
          data,
          siteId: executionContext.siteId
        }, auth);
      }
      break;

    default:
      throw new Error(`Unknown tools component: ${component}`);
  }
}

/**
 * Manejar operaciones de Triggers (consolidadas desde triggers cross-module)
 */
async function handleTriggersOperations(component, action, data, executionContext) {
  const { siteId } = executionContext;

  switch (component) {
    case 'sales':
      if (action === 'created') {
        // Consolidado desde onSaleCreated trigger
        return await processSaleCreatedTrigger(siteId, data);
      }
      break;

    case 'contacts':
      if (action === 'updated') {
        // Consolidado desde onContactUpdated trigger
        return await processContactUpdatedTrigger(siteId, data);
      }
      break;

    case 'products':
      if (action === 'updated') {
        // Consolidado desde onProductUpdated trigger
        return await processProductUpdatedTrigger(siteId, data);
      }
      break;

    default:
      throw new Error(`Unknown triggers component: ${component}`);
  }
}



/**
 * Procesar trigger de Sale Created
 */
async function processSaleCreatedTrigger(siteId, data) {
  // Importar lógica desde crossModuleTriggers
  const { processSaleCreation } = await import('../triggers/TriggerProcessors.js');
  return await processSaleCreation(siteId, data.saleId, data.saleData);
}

/**
 * Procesar trigger de Contact Updated
 */
async function processContactUpdatedTrigger(siteId, data) {
  const { processContactUpdate } = await import('../triggers/TriggerProcessors.js');
  return await processContactUpdate(siteId, data.contactId, data.beforeData, data.afterData);
}

/**
 * Procesar trigger de Product Updated
 */
async function processProductUpdatedTrigger(siteId, data) {
  const { processProductUpdate } = await import('../triggers/TriggerProcessors.js');
  return await processProductUpdate(siteId, data.productId, data.beforeData, data.afterData);
}

/**
 * Crear sesión completa para usuario siguiendo el big-picture corregido
 */
async function handleCreateUserSession(data, executionContext) {
  try {
    const { userId, siteId = 'main', firebaseUser } = data;

    if (!userId) {
      throw new Error('userId is required for createUserSession');
    }

    console.log('🔄 [Session] Iniciando creación de sesión para usuario:', userId, 'en site:', siteId);

    // Inicializar componentes necesarios
    const systemInitializer = new SystemInitializer();
    await systemInitializer.initialize();

    const hyperContext = systemInitializer.hyperContext;

    // ✅ PASO 1: Verificar/crear perfil global del usuario
    console.log('🔧 [Session] Verificando perfil global del usuario...');
    let globalUser = await hyperContext.get(`users[${userId}]`);

    if (!globalUser) {
      console.log('🆕 [Session] Creando perfil global del usuario...');

      // Crear perfil global independiente de cualquier site
      const globalUserData = {
        uid: userId,
        email: firebaseUser?.email || null,
        displayName: firebaseUser?.displayName || firebaseUser?.email?.split('@')[0] || 'Usuario',
        photoURL: firebaseUser?.photoURL || null,
        emailVerified: firebaseUser?.emailVerified || false,

        // Datos personales privados
        profile: {
          firstName: firebaseUser?.displayName?.split(' ')[0] || '',
          lastName: firebaseUser?.displayName?.split(' ').slice(1).join(' ') || '',
          language: 'es',
          timezone: 'America/Mexico_City'
        },

        // Preferencias globales
        preferences: {
          theme: 'auto',
          notifications: true,
          privacy: 'private'
        },

        // Metadatos
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'system',
          isFirstUser: false, // Se actualizará si es el primer usuario
          lastLoginAt: new Date().toISOString()
        }
      };

      await hyperContext.set(`users[${userId}]`, globalUserData);
      globalUser = globalUserData;
      console.log('✅ [Session] Perfil global creado');
    } else {
      // Actualizar última conexión
      globalUser.metadata.lastLoginAt = new Date().toISOString();
      globalUser.metadata.updatedAt = new Date().toISOString();
      await hyperContext.set(`users[${userId}]`, globalUser);
      console.log('✅ [Session] Perfil global actualizado');
    }

    // ✅ CORRECCIÓN: Actualizar o crear credencial para el site (evitar duplicaciones)
    console.log('🔧 [Session] Actualizando o creando credencial para el site...');

    const credentialData = {
      // Roles y permisos
      roles: isFirstUser ? ['super_admin'] : ['user'],
      permissions: isFirstUser ? { '*': true } : {},

      // Estado de la credencial
      status: {
        isActive: true,
        isVerified: true,
        isPrimary: true,
        expiresAt: null
      },

      // Información de la credencial
      name: isFirstUser ? 'Administrador Principal' : 'Usuario Estándar',
      description: isFirstUser ? 'Acceso completo al site con permisos administrativos' : 'Acceso básico al site',
      type: 'role_based',

      // Metadatos de la operación
      updatedBy: 'ProyectorV1Handler',
      reason: isFirstUser ? 'first_user_session_creation' : 'standard_user_session_creation',

      // Metadatos de la credencial
      metadata: {
        isFirstUser,
        assignmentReason: isFirstUser ? 'first_user_super_admin' : 'standard_user',
        assignmentContext: 'session_creation',
        createdBy: 'ProyectorV1Handler',
        lastModifiedBy: 'ProyectorV1Handler'
      }
    };

    // ✅ CORRECCIÓN: Usar updateOrCreateCredential para evitar duplicaciones
    const UserManager = require('../UserManager.js');
    const userManager = new UserManager(hyperContext, admin);
    const credential = await userManager.updateOrCreateCredential(userId, siteId, credentialData);
    console.log('✅ [Session] Credencial actualizada/creada:', credential.id);

    // ✅ PASO 3: Crear perfil del usuario en el site (usando arquitectura robusta)
    console.log('🔧 [Session] Creando perfil del usuario en el site...');
    const siteProfileData = {
      userId,
      siteId,

      // Datos específicos del site
      siteSpecificData: {
        displayName: globalUser.publicProfile?.displayName || globalUser.displayName,
        avatar: globalUser.publicProfile?.avatar || globalUser.photoURL,
        bio: isFirstUser ? 'Administrador principal del sistema' : '',
        department: isFirstUser ? 'Administración' : '',
        position: isFirstUser ? 'Super Admin' : 'Usuario',
        startDate: new Date().toISOString().split('T')[0],
        employeeId: isFirstUser ? 'ADMIN-001' : null
      },

      // Referencias a credenciales
      credentials: {
        primary: credentialId,
        additional: [],
        all: [credentialId]
      },

      // Roles y permisos agregados
      aggregatedRoles: credentialData.roles,
      aggregatedPermissions: credentialData.permissions,

      // Estado en el site
      status: {
        isActive: true,
        isVerified: true,
        isPending: false,
        lastActivity: new Date().toISOString(),
        joinedAt: new Date().toISOString()
      },

      // Metadatos
      metadata: {
        invitedBy: isFirstUser ? 'SystemInitializer' : null,
        isFirstUser,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.2.0'
      }
    };

    // Guardar perfil en sites[siteId].profiles[userId]
    await hyperContext.set(`sites[${siteId}].profiles[${userId}]`, siteProfileData);
    console.log('✅ [Session] Perfil del site creado en sites[].profiles[]');

    return {
      success: true,
      message: 'Sesión de usuario creada exitosamente',
      data: {
        userId,
        siteId,
        globalUser,
        credential: credentialData,
        siteProfile: siteProfileData,
        sessionCreatedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ [Session] Error creando sesión de usuario:', error);
    throw error;
  }
}

/**
 * Promover primer usuario a super-admin
 * @param {Object} data - Datos de la request
 * @param {Object} context - Contexto de la función
 * @returns {Promise<Object>} - Resultado de la operación
 */
export const handleAssignFirstUserAsSuperAdmin = async (data, context) => {
  try {
    console.log('🔄 [SuperAdmin] Iniciando promoción de primer usuario a super-admin');

    const { userId, siteId = 'main' } = data;

    if (!userId) {
      throw new Error('userId es requerido');
    }

    // Verificar que el usuario existe
    const globalUser = await hyperContext.get(`users[${userId}]`);
    if (!globalUser) {
      throw new Error('Usuario no encontrado');
    }

    // Verificar que es el primer usuario
    if (!globalUser.metadata?.isFirstUser) {
      throw new Error('Solo el primer usuario puede ser promovido a super-admin automáticamente');
    }

    // Obtener credencial actual del usuario
    const allCredentials = await hyperContext.get(`sites[${siteId}].credentials`);
    if (!allCredentials) {
      throw new Error('No se encontraron credenciales para el site');
    }

    const userCredentials = Object.values(allCredentials).filter(cred =>
      cred.userId === userId && cred.status?.isActive === true
    );

    if (userCredentials.length === 0) {
      throw new Error('No se encontró credencial activa para el usuario');
    }

    const credential = userCredentials[0];

    // ✅ PROMOCIÓN: Actualizar credencial con permisos de super-admin
    const updatedCredential = {
      ...credential,
      name: 'Super Administrador',
      description: 'Acceso completo al sistema con permisos administrativos máximos',

      // Roles de super-admin
      roles: ['owner', 'admin', 'editor', 'viewer', 'user'],

      // Permisos completos
      permissions: {
        'projects.create': true,
        'projects.read': true,
        'projects.update': true,
        'projects.delete': true,
        'users.invite': true,
        'users.remove': true,
        'users.modify_roles': true,
        'settings.site': true,
        'settings.billing': true,
        'settings.security': true,
        'ai.chat': true,
        'ai.analyze': true,
        'ai.configure': true,
        'modules.install': true,
        'modules.configure': true,
        'modules.remove': true,
        'system.backup': true,
        'system.restore': true,
        'system.maintenance': true,
        '*': true // Permisos universales
      },

      metadata: {
        ...credential.metadata,
        lastModifiedBy: 'ProyectorV1Handler',
        lastModifiedAt: new Date().toISOString(),
        version: (credential.metadata?.version || 1) + 1,
        changeHistory: [
          ...(credential.metadata?.changeHistory || []),
          {
            action: 'promoted_to_superadmin',
            timestamp: new Date().toISOString(),
            by: 'ProyectorV1Handler',
            reason: 'First user promotion to super-admin'
          }
        ],
        isSuperAdmin: true
      }
    };

    // Guardar credencial actualizada
    await hyperContext.set(`sites[${siteId}].credentials[${credential.id}]`, updatedCredential);

    // ✅ AUDITORÍA: Registrar promoción a super-admin usando nuevo sistema
    try {
      const userManager = proyector.getUserManager();
      await userManager.addCredentialChangeEntry(siteId, credential.id, {
        changedBy: 'ProyectorV1Handler',
        changes: ['role_promoted_to_super_admin', 'permissions_granted_full'],
        reason: 'first_user_super_admin_promotion',
        previousValues: {
          roles: credential.roles,
          permissions: credential.permissions
        },
        newValues: {
          roles: updatedCredential.roles,
          permissions: updatedCredential.permissions
        },
        affectedPermissions: Object.keys(updatedCredential.permissions || {})
      });
    } catch (auditError) {
      console.warn('⚠️ Error registrando auditoría de promoción:', auditError);
      // No fallar la promoción por error de auditoría
    }

    // ✅ ACTUALIZAR PERFIL DE SITE
    const siteProfile = await hyperContext.get(`sites[${siteId}].profiles[${userId}]`);
    if (siteProfile) {
      const updatedProfile = {
        ...siteProfile,
        siteSpecificData: {
          ...siteProfile.siteSpecificData,
          position: 'Super Administrador',
          department: 'Administración',
          employeeId: 'SUPERADMIN-001'
        },
        aggregatedRoles: updatedCredential.roles,
        aggregatedPermissions: updatedCredential.permissions,
        metadata: {
          ...siteProfile.metadata,
          updatedAt: new Date().toISOString(),
          isSuperAdmin: true
        }
      };

      await hyperContext.set(`sites[${siteId}].profiles[${userId}]`, updatedProfile);
    }

    // ✅ ACTUALIZAR USUARIO GLOBAL
    const updatedGlobalUser = {
      ...globalUser,
      metadata: {
        ...globalUser.metadata,
        isSuperAdmin: true,
        superAdminSince: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };

    await hyperContext.set(`users[${userId}]`, updatedGlobalUser);

    console.log('✅ [SuperAdmin] Usuario promovido exitosamente a super-admin');

    return {
      success: true,
      message: 'Usuario promovido exitosamente a super-admin',
      data: {
        userId,
        siteId,
        credential: updatedCredential,
        promotedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ [SuperAdmin] Error promoviendo usuario a super-admin:', error);
    throw error;
  }
};

/**
 * Generar ID único para requests
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// Las funciones ya están exportadas con 'export const' en sus declaraciones
