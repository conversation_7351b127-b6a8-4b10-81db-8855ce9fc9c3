# Actual por desarrollar

Excelente avance! realizaré las pruebas posteriormente, replantea tu concepción los siguientes temas presentados.

Templates, tienes que comprender que una cosa son los templates compuesto por html directamente con código de react normal, que va a compañado con controllers que mediante el sistema unificado en zustand encapsulan todo lo referente a fetch y procesos complicados simplificando así el despligue de complejas interfaces. Son utilizados por los specs de manera declarativa (se almacena en db y se cachea mediante hypercontext de multiples maneras al igual que los codeblocks y modules (los modules son specs)) que son los objetos json o literales, que a su vez se renderizan por medio de los pageblocks/layouts en donde los layouts permiten recursividad y personalización mediante los controllers, los blocks entonces son la conexión entre los controllers y templates, que proveen de una interfaz mediante los specs a los modules para que también mediante controllers/pageblocks/layouts/demás se pueda encapsular de manera estática y dinámica y cachear según el caso, todo dentro del Hypercontext, en la DB se guardan siempre todo por site, a los codeblocks y specs específcamente, entre los specs están los dichos modules (conjuntos de otros specs como: entities, datafields, fieldsets (estos 3 conceptos citados previamente son sumamente importante por que de él se obtiene la composición de entidades que son lo que se despliega en los pages mediante todo lo demás en el Proyector, a partir de estos se crean los indices ya sean vectoriales como también indices normales con sus respectivas convinaciones y demás directo en firebase), codeblocks (ciertos metadatos), pages (concebimos a pages como las url públicas y las del panel), aiTools, etc.).

Codeblocks, son los conjuntos de código empaquetados traidos desde el cache en storage, que se ejecutan en el frontend y en el backend mediante slots de ejecución que determinan a su vez el respectivo schema de input y output. Mediante los codeblocks se deben poder ejecutar flujos de conversación, formularios, validaciones, transformaciones, orquestación de procesos, configuración dinámica, aiTools, botAgents, botFlows, pages, template html react de blocks con sus controllers, etc. 

Entonces así los modules serían los specs más principales que referencian a: codeblocks, pages url, aiTools, botAgents, botFlows, menu sections, que podrían o no, tener a su vez más specs almacenados.

Modos menu top: Harmony (chat con botAgents), current site (por defecto main site), user's sections.
El menu top debes investigar en src\modules\panel\MenuMainContent.js y demás del legacy, básicamente es como en vscode o canvas que tienen un menu vertical a la izquierda siendo el nivel superior de menu, el objetivo es que al presionar:
- Harmony: se despliega el chat con los botagents y se oculta el menu lateral, al cerrar el chat se vuelve a mostrar el menu lateral.
- current site: se despliega el menu lateral con las secciones del site actual, en los specs de los modules se puede configurar tanto el orden como la visibilidad de las secciones, ademas de que se puede configurar el icono y el título de cada sección, ademas de que se puede configurar el orden de los items de cada sección, ademas de que se puede configurar el orden de las secciones, ademas de que se puede configurar si la sección es collapsible o no, ademas de que se puede configurar si la sección es visible para todos los usuarios o solo para los administradores, etc. 
- user's sections: se despliega el menu lateral con las secciones del usuario (mis sites, datos personales, etc.).

Ahora el chat se muestra a la derecha y durante los procesos previos se perdió la riqueza que tenía en cuanto a ui del chat, en el legacy omnichat y messages y demás podrás ver inspiración, pero sólo inspiración por que debes comprender que, en el track de mensajes cada mensaje corresponde a un doc en el hypercontext, dicho doc de mensaje que es el recipiente del state de los botFlows triggeados y de los aiTools ejecutados. La riquieza visual de los mensajes es clave para poder aprovechar al máximo la arquitectura del Proyector. Entonces el Chat Track debe ser un layout de pageblocks, y los mensajes a su vez blocks, siendo específicamente staticCodeblocks y staticModules según corresponda en relación al resto del Proyector.

Métódicamente mediante varios tasks que reinteradamente indica a que se investigue aún más robustamente sobre estos temas y se actualicen los docs, comprende mediante la interconexión de los conceptos en su uso y en su propósito individual, para luego actualizar el docs\Proyector\v1.2\PROYECTOR-V1.2-REFERENCE.md orientando así a los agentes sobre las prácticas, maneras y atajos del frontend.

También debes para cada tema de remodelamiento ya de la estética visual del Proyector, debes para cada cosa ver primero durante la investigación tanto qué ya hay hecho, de qué manera, ver según el big-picture y a las aclaraciones técnicas de párrafos previos, si es qué está alineado o no, y así expresar en un nuevo .md los checks y planes con referencias y explicaciones detalladas de cada cosa, para luego implementarlos de manera ordenada y coherente considerando en cada tasks a las partes del Proyector, referenciando cada concepto a su respectivo docs o sección del PROYECTOR-V1.2-REFERENCE.md

hay archivos a ser eliminados y hechos de vuelta pero de las maneras correctas me refiero a public\staticCodeblocks\branding-editor\template.json y 

El PROYECTOR-V1.2-REFERENCE.md debe cumplir la función de resumen óptimo del big-picture del Proyector v1.2.

También quiero además de que esté bien el menú top, menús lateralos y sus contenidos reales a partir del HyperContext (db + cache) y el tema del chat (el chat ya no estará entonces a la derecha, estará en el lateral izquierdo en la parte inferior del layout del chat track, que a su vez todo eso estara en una sección del menu top) con todos los chiches para manejar agentes botAgents, quiero también que identifiques, idees y presentes la jerarquía de rutas publicas y del panel en base a todo esto explicado, para luego implementarlos de manera ordenada y coherente considerando en cada tasks a las partes del Proyector, referenciando cada concepto a su respectivo docs o sección del PROYECTOR-V1.2-REFERENCE.md

No actues precipitadamente, centrate en investigar y reunir los elementos prácticos para luego implementarlos de manera ordenada y coherente considerando en cada tasks a las partes del Proyector, 

---

# Anteriores aún sin comprobar su solución

entre en /new por primera vez y quise crear mi user super admin para el main site 

Se detectaron varias incoherencias:
- Se quedó colgado en "cargando ruta" luego de haber pedido el primer login.
- Se usan mal las credenciales
- Siguen existiendo rutas del hypercontext en la raíz que ya no deberían estar como /HyperContext_dev/settings/settings y /HyperContext_dev/permissions/permissions debido a que ya estan implementados como parte de los sites, y el main site equivale a lo que serían los configs "globales"
- Actualmente están /new y /proyector, pero /proyector no debería existir, y /new debería ser el root "/" y en todos los casos debe mostrarse el side menu


Consideraciones
- La nomenclatura de paths del hypercontext es distinta a la de firebase, atendé
- Entiende la arquitectura de sites, auth, codeblocks, modules


Hypercontext

se crearon dobles credentials de manera incoherente, si bien uno fue creado al principio al iniciar sesión por primera vez, y el segundo corresponde a cuando se inicializó el main site asignando al current user (recién logeado por primera vez) como el super-admin del main site, pero lo hizo mal por que en realidad simplemente la misma credencial debería haberse actualizado, el tema es que cada user puede tener sólo una credencial por site (credencial que puede tener multiples roles, los roles son los que especifican los permissions)
(firebase path) /HyperContext_dev/sites/sites/main/credentials/V51uAyfjy7WgQ08UbmvkDFhQ9VyK
(firebase path) /HyperContext_dev/sites/sites/main/credentials/V51uAyfjy7WgQ08UbmvkDFhQ9VyK_1751500473986_6nnh8

