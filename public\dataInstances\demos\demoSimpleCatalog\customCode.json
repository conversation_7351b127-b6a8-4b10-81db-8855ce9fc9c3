{"components": [{"name": "ProductCard", "type": "uiComponent", "purpose": "Tarjeta de producto para mostrar en listados y grillas", "module": "cart", "entity": "cartItems", "extensionPoint": "ListItemCard", "implementation": "Componente React que muestra la información principal del producto con imagen, precio y botón de añadir al carrito"}, {"name": "CategoryNavigation", "type": "uiComponent", "purpose": "Navegación por categorías en la barra lateral", "module": "cart", "entity": "cartItemCategories", "extensionPoint": "SideMenuSectionHeader", "implementation": "Componente React que muestra un árbol de navegación por categorías con soporte para subcategorías"}, {"name": "CartSummary", "type": "uiComponent", "purpose": "Resumen del carrito en la barra superior", "module": "cart", "entity": "carts", "extensionPoint": "HeaderToolbarRight", "implementation": "Componente React que muestra un resumen del carrito con cantidad de items y total"}, {"name": "ProductFilterSidebar", "type": "uiComponent", "purpose": "Barra lateral de filtros para productos", "module": "cart", "entity": "cartItems", "extensionPoint": "ListSidebar", "implementation": "Componente React que permite filtrar productos por categoría, precio, disponibilidad y atributos"}, {"name": "FeaturedProductsCarousel", "type": "uiComponent", "purpose": "Carrusel de productos destacados para la página principal", "module": "layoutHome", "entity": "homeLayouts", "extensionPoint": "HomeSection", "implementation": "Componente React que muestra un carrusel de productos destacados con navegación y autoplay"}, {"name": "UnderListItemTitleForCartItems", "type": "crudComponent", "purpose": "Mostrar información adicional debajo del título del producto en la lista", "module": "cart", "entity": "cartItems", "extensionPoint": "UnderListItemTitle", "implementation": "Componente que muestra la imagen principal, precio, categoría y disponibilidad del producto"}, {"name": "VariantsCRUD", "type": "crudComponent", "purpose": "Gestionar variantes del producto después del campo de categoría", "module": "cart", "entity": "cartItems", "extensionPoint": "FormAppend-itemCategoryId", "implementation": "Componente que permite crear, editar y eliminar variantes del producto"}, {"name": "FormToolbarEndCartItems", "type": "crudComponent", "purpose": "Añadir botones adicionales en la barra de herramientas del formulario", "module": "cart", "entity": "cartItems", "extensionPoint": "FormToolbarEnd", "implementation": "Botón para ver el producto en la tienda pública"}, {"name": "ListHeaderExtraActionsCartItems", "type": "crudComponent", "purpose": "Mostrar información sobre límites del plan en el encabezado de la lista", "module": "cart", "entity": "cartItems", "extensionPoint": "ListHeaderExtraActions", "implementation": "Componente que muestra alertas sobre límites de productos y botón para aumentar plan"}, {"name": "ListHeaderExtraActionsCategories", "type": "crudComponent", "purpose": "Mostrar acciones adicionales en el encabezado de la lista de categorías", "module": "cart", "entity": "cartItemCategories", "extensionPoint": "ListHeaderExtraActions", "implementation": "Componente que muestra botones para importar/exportar categorías y reorganizar la estructura"}, {"name": "CategoryHierarchyViewer", "type": "crudComponent", "purpose": "Mostrar la jerarquía de categorías en el formulario", "module": "cart", "entity": "cartItemCategories", "extensionPoint": "FormAppendColumnRight", "implementation": "Componente que muestra un árbol visual de la estructura de categorías"}, {"name": "UnderListItemTitleForCarts", "type": "crudComponent", "purpose": "Mostrar información adicional debajo del título del carrito en la lista", "module": "cart", "entity": "carts", "extensionPoint": "UnderListItemTitle", "implementation": "Componente que muestra el ID del carrito y la fecha de creación formateada"}, {"name": "ShowItemExtraActionsCarts", "type": "crudComponent", "purpose": "Mostrar acciones adicionales en la vista detallada del carrito", "module": "cart", "entity": "carts", "extensionPoint": "ShowItemExtraActions", "implementation": "Componente que muestra botones para procesar el carrito, enviar por WhatsApp, etc."}], "dataTypes": [{"name": "dataTypeBlockStack", "purpose": "Gestionar la estructura de bloques para construcción de páginas", "module": "blockStack", "entity": "pages", "fieldName": "stack", "implementation": "Tipo de datos personalizado que permite construir páginas mediante bloques arrastrables con diferentes tipos de contenido"}, {"name": "dataTypeFields", "purpose": "Gestionar campos personalizados para productos", "module": "cart", "entity": "<PERSON><PERSON><PERSON><PERSON>ields", "fieldName": "fields", "implementation": "Tipo de datos personalizado que permite definir y gestionar campos personalizados para productos según su categoría"}], "events": [{"entity": "cartItems", "events": [{"name": "generateProductSlug", "type": "beforeSave", "purpose": "Generar automáticamente un slug para los productos basado en su nombre", "module": "cart", "implementation": "Función que genera un slug único para cada producto basado en su nombre"}, {"name": "updateRelatedProducts", "type": "afterSave", "purpose": "Actualizar productos relacionados cuando cambia la categoría", "module": "cart", "implementation": "Función que actualiza las referencias a productos relacionados cuando un producto cambia de categoría"}]}, {"entity": "carts", "events": [{"name": "updateProductStock", "type": "afterSave", "purpose": "Actualizar stock de productos cuando se añaden al carrito", "module": "cart", "implementation": "Función que actualiza el stock disponible de los productos cuando se añaden al carrito"}, {"name": "restoreProductStock", "type": "beforeDelete", "purpose": "Restaurar el stock de productos si se elimina un carrito", "module": "cart", "implementation": "Función que restaura el stock de productos si se elimina un carrito antes de completar la compra"}]}, {"entity": "cartItemCategories", "events": [{"name": "updateCategoryTree", "type": "afterSave", "purpose": "Actualizar el árbol de categorías cuando se modifica una categoría", "module": "cart", "implementation": "Función que actualiza la estructura jerárquica de categorías cuando se añade, modifica o elimina una categoría"}, {"name": "reassignProducts", "type": "afterDelete", "purpose": "Reasignar productos a categoría por defecto si se elimina su categoría", "module": "cart", "implementation": "Función que reasigna productos a una categoría por defecto cuando se elimina la categoría a la que pertenecían"}]}], "routes": {"module": [{"name": "ProductCatalog", "type": "routesPublic", "purpose": "Página de catálogo de productos con filtros y paginación", "module": "pages", "entitySlug": "products", "action": "filter", "implementation": "Ruta pública de módulo que muestra el catálogo de productos con opciones de filtrado y paginación", "url": "/:instance/m/products/filter", "component": "RouteProductFilter"}, {"name": "ProductDetail", "type": "routesPublic", "purpose": "Página de detalle de producto con información completa y opciones de compra", "module": "pages", "entitySlug": "products", "action": "view", "implementation": "Ruta pública de módulo que muestra el detalle completo de un producto con galería de imágenes, descripción, variantes y botón de añadir al carrito", "url": "/:instance/m/products/view/#/productId/:productId", "component": "RouteProductView"}, {"name": "ShoppingCart", "type": "routesPublic", "purpose": "Página de carrito de compras con resumen y opciones de checkout", "module": "pages", "entitySlug": "carts", "action": "view", "implementation": "Ruta pública de módulo que muestra el contenido del carrito con opciones para modificar cantidades, eliminar productos y proceder al checkout", "url": "/:siteSlug/m/carts/view", "component": "RouteCartView"}, {"name": "AdminDashboard", "type": "routesAdmin", "purpose": "Panel de administración principal", "module": "panel", "entitySlug": "dashboard", "action": "view", "implementation": "Ruta de administración de módulo que muestra el panel principal con estadísticas y accesos rápidos", "url": "/:siteSlug/dashboard/view", "component": "AdminTabHome"}], "blocks": [{"name": "HomePage", "type": "blockStack", "purpose": "Página principal con bloques de contenido", "module": "blockStack", "pageUrl": "home", "implementation": "Página construida con el sistema de bloques que muestra la página principal del sitio", "url": "/:instance/m/p/home", "component": "Route<PERSON><PERSON>"}, {"name": "CatalogPage", "type": "blockStack", "purpose": "Página de catálogo de productos con bloques personalizados", "module": "blockStack", "pageUrl": "items", "implementation": "Página construida con el sistema de bloques que muestra el catálogo de productos", "url": "/:instance/m/p/items", "component": "Route<PERSON><PERSON>"}, {"name": "ProductDetailPage", "type": "blockStack", "purpose": "Página de detalle de producto con bloques personalizados", "module": "blockStack", "pageUrl": "item-details", "implementation": "Página construida con el sistema de bloques que muestra el detalle de un producto", "url": "/:instance/m/p/item-details#/itemId/:itemId", "component": "Route<PERSON><PERSON>"}, {"name": "CartPage", "type": "blockStack", "purpose": "Página de carrito de compras con bloques personalizados", "module": "blockStack", "pageUrl": "cart", "implementation": "Página construida con el sistema de bloques que muestra el carrito de compras", "url": "/:instance/m/p/cart", "component": "Route<PERSON><PERSON>"}], "entityCrudConfig": [{"name": "cartItems", "type": "entity", "purpose": "Gestión de productos del catálogo", "operations": ["list", "create", "update", "show", "delete"], "customizations": {"settings": {"redirectOnSave": "form", "queryToCountForLimits": {"mainAvailable": "true", "deleted": "false"}, "fieldForCountLimit": "maxItems"}, "components": ["UnderListItemTitleForCartItems", "VariantsCRUD", "FormToolbarEndCartItems", "ListHeaderExtraActionsCartItems"]}}, {"name": "cartItemCategories", "type": "entity", "purpose": "Gestión de categorías de productos", "operations": ["list", "create", "update", "delete"], "customizations": {"settings": {"redirectOnSave": "list", "reorder": true, "showBtnMove": true}, "components": ["ListHeaderExtraActionsCategories", "CategoryHierarchyViewer"]}}, {"name": "<PERSON><PERSON><PERSON><PERSON>ields", "type": "entity", "purpose": "Gestión de campos personalizados para productos", "operations": ["list", "create", "update"], "customizations": {"settings": {"redirectOnSave": "list", "reorder": true}, "codedDataTypes": ["dataTypeFields"]}}, {"name": "carts", "type": "entity", "purpose": "Visualización de carritos de compra", "operations": ["list", "show"], "customizations": {"settings": {"showBtnAdd": false, "showBtnDelete": false, "showBtnUpdate": false}, "components": ["UnderListItemTitleForCarts", "ShowItemExtraActionsCarts"]}}]}}