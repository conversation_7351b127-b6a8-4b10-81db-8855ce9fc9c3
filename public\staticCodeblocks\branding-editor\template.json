{"component": "container", "className": "branding-editor-container", "children": [{"component": "IonCard", "className": "branding-editor-card", "children": [{"component": "IonCardHeader", "children": [{"component": "container", "className": "header-content", "children": [{"component": "IonCardTitle", "children": [{"component": "container", "className": "title-with-icon", "children": [{"component": "IonIcon", "props": {"icon": "colorPaletteOutline", "className": "title-icon"}}, {"component": "text", "content": "Editor de Branding"}]}]}, {"component": "container", "className": "header-actions", "children": [{"component": "IonButton", "props": {"fill": "clear", "size": "small", "onClick": "{{controller.togglePreview}}", "disabled": "{{controller.loading}}"}, "children": [{"component": "IonIcon", "props": {"icon": "{{controller.isPreviewMode ? 'eyeOffOutline' : 'eyeOutline'}}", "slot": "start"}}, {"component": "text", "content": "{{controller.isPreviewMode ? 'Desactivar Preview' : 'Activar Preview'}}"}]}]}]}]}, {"component": "IonCardContent", "children": [{"component": "conditional", "condition": "{{controller.loading}}", "children": [{"component": "container", "className": "loading-container", "children": [{"component": "Ion<PERSON><PERSON><PERSON>", "props": {"name": "crescent"}}, {"component": "text", "content": "Cargando configuración de branding...", "className": "loading-text"}]}]}, {"component": "conditional", "condition": "{{!controller.loading && !controller.error}}", "children": [{"component": "TabbedFormBlock", "props": {"formData": "{{controller.formData}}", "onChange": "{{controller.handleFormChange}}", "schema": {"tabs": [{"id": "colors", "label": "Colores", "icon": "colorPaletteOutline", "fields": [{"name": "primary_color", "type": "color", "label": "Color Primario", "description": "Color principal del tema", "required": true}, {"name": "secondary_color", "type": "color", "label": "Color Secundario", "description": "Color secundario del tema", "required": true}, {"name": "accent_color", "type": "color", "label": "Color de Acento", "description": "Color de acento para elementos destacados", "required": true}]}, {"id": "typography", "label": "Tipografía", "icon": "textOutline", "fields": [{"name": "primary_font", "type": "select", "label": "Fuente Primaria", "description": "Fuente principal para títulos y elementos importantes", "options": [{"value": "Inter", "label": "Inter"}, {"value": "Roboto", "label": "Roboto"}, {"value": "Open Sans", "label": "Open Sans"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Montserrat", "label": "Montserrat"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}], "required": true}, {"name": "secondary_font", "type": "select", "label": "Fuente Secundaria", "description": "Fuente para texto de contenido", "options": [{"value": "Inter", "label": "Inter"}, {"value": "Roboto", "label": "Roboto"}, {"value": "Open Sans", "label": "Open Sans"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Source Sans Pro", "label": "Source Sans Pro"}], "required": true}]}, {"id": "images", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": "imageOutline", "fields": [{"name": "logo", "type": "file", "label": "Logo", "description": "Logo principal del site", "accept": "image/*", "maxSize": "2MB", "multiple": false}, {"name": "favicon", "type": "file", "label": "Favicon", "description": "Icono que aparece en la pestaña del navegador", "accept": "image/x-icon,image/png", "maxSize": "1MB", "multiple": false}]}, {"id": "theme", "label": "<PERSON><PERSON>", "icon": "brushOutline", "fields": [{"name": "theme_style", "type": "select", "label": "<PERSON><PERSON><PERSON>", "description": "Estilo general del tema", "options": [{"value": "modern", "label": "Moderno"}, {"value": "classic", "label": "Clásico"}, {"value": "minimal", "label": "Minimalista"}, {"value": "bold", "label": "Audaz"}], "required": true}]}]}}}]}, {"component": "conditional", "condition": "{{controller.error}}", "children": [{"component": "container", "className": "error-container", "children": [{"component": "IonIcon", "props": {"icon": "alertCircleOutline", "className": "error-icon"}}, {"component": "text", "content": "{{controller.error}}", "className": "error-text"}, {"component": "IonButton", "props": {"fill": "clear", "size": "small", "onClick": "{{controller.loadBrandingData}}"}, "children": [{"component": "text", "content": "Reintentar"}]}]}]}]}]}, {"component": "container", "className": "actions-container", "children": [{"component": "container", "className": "actions-left", "children": [{"component": "IonButton", "props": {"fill": "outline", "color": "medium", "onClick": "{{controller.resetToDefault}}", "disabled": "{{controller.loading}}"}, "children": [{"component": "IonIcon", "props": {"icon": "refreshOutline", "slot": "start"}}, {"component": "text", "content": "Resetear"}]}]}, {"component": "container", "className": "actions-right", "children": [{"component": "IonButton", "props": {"fill": "clear", "color": "medium", "onClick": "{{controller.cancelChanges}}", "disabled": "{{controller.loading}}"}, "children": [{"component": "text", "content": "<PERSON><PERSON><PERSON>"}]}, {"component": "IonButton", "props": {"color": "primary", "onClick": "{{controller.saveChanges}}", "disabled": "{{controller.loading || !controller.isDirty}}"}, "children": [{"component": "IonIcon", "props": {"icon": "saveOutline", "slot": "start"}}, {"component": "text", "content": "{{controller.loading ? 'Guardando...' : 'Guardar Cambios'}}"}]}]}]}]}