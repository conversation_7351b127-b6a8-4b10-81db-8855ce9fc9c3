{"slug": "chat-track-layout", "id": "chat-track-layout", "name": "Chat Track Layout", "version": "1.0.0", "description": "Layout de PageBlocks para chat track donde cada mensaje es un block individual", "type": "static-codeblock", "category": "chat-layout", "tags": ["chat", "layout", "pageblocks", "messages", "track"], "author": "Proyector v1.2 Team", "created": "2025-01-22", "updated": "2025-01-22", "framework": "react", "location": "public", "metadata": {"framework": "React", "dependencies": ["@ionic/react", "framer-motion", "firebase"], "responsive": true, "animated": true, "realtime": true, "performance": "optimized", "accessibility": "wcag-aa"}, "props": {"conversationId": {"type": "string", "required": true, "description": "ID de la conversación"}, "conversationType": {"type": "string", "default": "user_to_bot", "enum": ["user_to_user", "user_to_bot", "bot_to_bot", "group_chat", "support_chat", "contextual_chat"], "description": "Tipo de conversación"}, "layout": {"type": "string", "default": "vertical", "enum": ["vertical", "threaded", "compact"], "description": "Layout del chat track"}, "enableRealtime": {"type": "boolean", "default": true, "description": "Habilitar actualizaciones en tiempo real"}, "enableAnimations": {"type": "boolean", "default": true, "description": "Habilitar animaciones de mensajes"}, "maxMessages": {"type": "number", "default": 100, "description": "Máximo número de mensajes a cargar"}, "enableVirtualization": {"type": "boolean", "default": true, "description": "Habilitar virtualización para performance"}}, "controller": {"file": "component.jsx", "type": "react-controller", "exports": ["ChatTrackLayoutController"]}, "template": {"type": "pageblock-layout", "renderer": "Universal<PERSON><PERSON><PERSON>", "layout": "chat-track-recursive", "supports": {"messageBlocks": true, "recursiveLayouts": true, "dynamicBlocks": true}}, "styles": {"file": "styles.css", "type": "css", "scope": "component"}, "files": {"component": "./component.jsx", "config": "./codeblock.json", "styles": "./styles.css"}, "configuration": {"enableLazyLoading": true, "enableVirtualization": true, "cacheMessages": true, "enableAnalytics": true, "realtimeSync": true}, "integrations": {"store": {"slice": "chatSlice", "actions": ["loadMessages", "sendMessage", "updateMessage"]}, "api": {"endpoints": ["/api/conversations/{conversationId}/messages", "/api/conversations/{conversationId}/realtime"]}, "hypercontext": {"paths": ["botConversations[conversationId]", "botConversationsMessages[conversationId]", "sites[siteId].conversations[conversationId]"]}, "firebase": {"collections": ["botConversations", "botConversationsMessages"], "realtime": true}}, "messageBlocks": {"text": {"codeblock": "message-text-block", "description": "Mensaje de texto simple"}, "pageblocks": {"codeblock": "message-pageblocks-block", "description": "Mensaje con contenido PageBlocks"}, "rich_text": {"codeblock": "message-rich-text-block", "description": "Mensaje con texto enriquecido"}, "media": {"codeblock": "message-media-block", "description": "Mensaje con archivos multimedia"}, "interactive": {"codeblock": "message-interactive-block", "description": "Mensaje con elementos interactivos"}, "system": {"codeblock": "message-system-block", "description": "Mensaje del sistema"}, "tool_result": {"codeblock": "message-tool-result-block", "description": "Resultado de herramienta"}, "bot_agent": {"codeblock": "message-bot-agent-block", "description": "Mensaje de bot agent"}}, "permissions": {"view": ["conversation:view", "chat:view"], "edit": ["conversation:edit", "chat:edit"], "admin": ["conversation:admin", "chat:admin"]}, "responsive": {"breakpoints": {"mobile": "max-width: 768px", "tablet": "max-width: 992px", "desktop": "min-width: 993px"}, "layouts": {"mobile": "compact", "tablet": "vertical", "desktop": "vertical"}}, "performance": {"virtualScrolling": true, "lazyLoading": true, "messagePooling": true, "realtimeOptimization": true}}