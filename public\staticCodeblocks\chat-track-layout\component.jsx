/**
 * Chat Track Layout Controller
 * 
 * Layout de PageBlocks para chat track donde cada mensaje es un block individual.
 * Implementa la arquitectura recursiva de pageblocks para mensajes.
 */

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  IonContent,
  IonList,
  IonItem,
  IonSpinner,
  IonNote,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  IonRefresher,
  IonRefresherContent,
  IonVirtualScroll
} from '@ionic/react';

// Firebase
import { onSnapshot, query, orderBy, limit, startAfter } from 'firebase/firestore';

// Store hooks
import { useAuth, useSite } from '../../../src/proyector/store';

// UniversalRenderer para renderizar message blocks
import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';

// Estilos
import './styles.css';

// Motion
import { motion, AnimatePresence, LayoutGroup } from 'framer-motion';

/**
 * Chat Track Layout Controller
 */
export function ChatTrackLayoutController({ 
  conversationId,
  conversationType = 'user_to_bot',
  layout = 'vertical',
  enableRealtime = true,
  enableAnimations = true,
  maxMessages = 100,
  enableVirtualization = true,
  className = ''
}) {
  // Store hooks
  const { user } = useAuth();
  const { current: currentSite } = useSite();

  // Estado local
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [lastVisible, setLastVisible] = useState(null);

  // Refs
  const messagesEndRef = useRef(null);
  const unsubscribeRef = useRef(null);

  // Configuración de mensaje blocks
  const messageBlockTypes = useMemo(() => ({
    text: 'message-text-block',
    pageblocks: 'message-pageblocks-block',
    rich_text: 'message-rich-text-block',
    media: 'message-media-block',
    interactive: 'message-interactive-block',
    system: 'message-system-block',
    tool_result: 'message-tool-result-block',
    bot_agent: 'message-bot-agent-block'
  }), []);

  // Cargar mensajes iniciales
  useEffect(() => {
    if (!conversationId) return;

    loadMessages();

    // Configurar realtime si está habilitado
    if (enableRealtime) {
      setupRealtimeListener();
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [conversationId, enableRealtime]);

  // Auto-scroll a nuevos mensajes
  useEffect(() => {
    if (enableAnimations && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length, enableAnimations]);

  // Cargar mensajes
  const loadMessages = async (loadMore = false) => {
    try {
      setLoading(!loadMore);

      // TODO: Implementar carga desde Firestore
      // const messagesRef = collection(db, 'botConversationsMessages', conversationId, 'messages');
      // const q = query(messagesRef, orderBy('timestamp', 'desc'), limit(maxMessages));
      
      // Simulación de datos para desarrollo
      const mockMessages = generateMockMessages(conversationId, 10);
      
      if (loadMore) {
        setMessages(prev => [...prev, ...mockMessages]);
      } else {
        setMessages(mockMessages);
      }

      setHasMore(mockMessages.length === maxMessages);
    } catch (err) {
      console.error('Error cargando mensajes:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Configurar listener de tiempo real
  const setupRealtimeListener = () => {
    // TODO: Implementar listener de Firestore
    // const messagesRef = collection(db, 'botConversationsMessages', conversationId, 'messages');
    // const q = query(messagesRef, orderBy('timestamp', 'desc'), limit(maxMessages));
    
    // unsubscribeRef.current = onSnapshot(q, (snapshot) => {
    //   const newMessages = snapshot.docs.map(doc => ({
    //     id: doc.id,
    //     ...doc.data()
    //   }));
    //   setMessages(newMessages.reverse());
    // });

    console.log('Realtime listener configurado para:', conversationId);
  };

  // Generar mensajes mock para desarrollo
  const generateMockMessages = (convId, count) => {
    const types = Object.keys(messageBlockTypes);
    const messages = [];

    for (let i = 0; i < count; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      messages.push({
        id: `msg-${convId}-${i}`,
        type,
        content: `Mensaje ${i + 1} de tipo ${type}`,
        senderId: i % 2 === 0 ? user?.uid || 'user-1' : 'bot-agent-1',
        timestamp: new Date(Date.now() - (count - i) * 60000),
        status: 'delivered',
        metadata: {
          conversationId: convId,
          conversationType,
          isOwn: i % 2 === 0
        }
      });
    }

    return messages;
  };

  // Manejar carga infinita
  const handleInfiniteScroll = useCallback(async (event) => {
    if (hasMore) {
      await loadMessages(true);
    }
    event.target.complete();
  }, [hasMore]);

  // Manejar refresh
  const handleRefresh = useCallback(async (event) => {
    await loadMessages(false);
    event.detail.complete();
  }, []);

  // Obtener configuración del message block
  const getMessageBlockConfig = (message) => {
    const isOwn = message.senderId === user?.uid;
    
    return {
      message,
      isOwn,
      conversationId,
      conversationType,
      layout,
      enableAnimations,
      // Configuración específica por tipo
      ...(message.type === 'pageblocks' && {
        pageblocks: message.pageblocks || [],
        enableRecursive: true
      }),
      ...(message.type === 'tool_result' && {
        toolName: message.toolName,
        toolResult: message.toolResult
      }),
      ...(message.type === 'bot_agent' && {
        agentId: message.agentId,
        agentName: message.agentName
      })
    };
  };

  // Renderizar mensaje individual
  const renderMessage = (message, index) => {
    const blockType = messageBlockTypes[message.type] || messageBlockTypes.text;
    const config = getMessageBlockConfig(message);

    return (
      <motion.div
        key={message.id}
        initial={enableAnimations ? { opacity: 0, y: 20 } : false}
        animate={enableAnimations ? { opacity: 1, y: 0 } : false}
        transition={enableAnimations ? { delay: index * 0.05 } : false}
        className="message-block-container"
      >
        <UniversalRenderer
          blockType={blockType}
          config={config}
          enableAnimations={enableAnimations}
          enableErrorBoundary={true}
        />
      </motion.div>
    );
  };

  if (loading && messages.length === 0) {
    return (
      <div className="chat-track-loading">
        <IonSpinner name="crescent" />
        <IonNote>Cargando conversación...</IonNote>
      </div>
    );
  }

  if (error) {
    return (
      <div className="chat-track-error">
        <IonNote color="danger">Error: {error}</IonNote>
      </div>
    );
  }

  return (
    <div className={`chat-track-layout ${layout} ${className}`}>
      <IonContent className="chat-track-content">
        {/* Refresher */}
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent />
        </IonRefresher>

        {/* Messages Container */}
        <div className="messages-container">
          {enableVirtualization && messages.length > 50 ? (
            // Virtual scroll para performance con muchos mensajes
            <IonVirtualScroll
              items={messages}
              approxItemHeight={100}
              renderItem={(message, index) => (
                <IonItem key={message.id} className="virtual-message-item">
                  {renderMessage(message, index)}
                </IonItem>
              )}
            />
          ) : (
            // Renderizado normal
            <LayoutGroup>
              <AnimatePresence>
                {messages.map((message, index) => renderMessage(message, index))}
              </AnimatePresence>
            </LayoutGroup>
          )}

          {/* Scroll anchor */}
          <div ref={messagesEndRef} className="messages-end-anchor" />
        </div>

        {/* Infinite scroll */}
        {hasMore && (
          <IonInfiniteScroll onIonInfinite={handleInfiniteScroll}>
            <IonInfiniteScrollContent loadingText="Cargando más mensajes..." />
          </IonInfiniteScroll>
        )}
      </IonContent>
    </div>
  );
}

export default ChatTrackLayoutController;
