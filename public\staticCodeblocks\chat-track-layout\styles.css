/**
 * Chat Track Layout Styles
 * 
 * Estilos para el layout de chat track con pageblocks recursivos
 */

/* Container principal */
.chat-track-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--ion-background-color, #ffffff);
}

/* Content del chat */
.chat-track-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
}

/* Container de mensajes */
.messages-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  min-height: 100%;
}

/* Message block container */
.message-block-container {
  width: 100%;
  position: relative;
}

/* Anchor para scroll */
.messages-end-anchor {
  height: 1px;
  width: 100%;
}

/* Estados de carga */
.chat-track-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

.chat-track-loading ion-spinner {
  --color: var(--ion-color-primary);
}

.chat-track-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  padding: 20px;
  text-align: center;
}

/* Virtual scroll items */
.virtual-message-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 0;
  --inner-padding-end: 0;
  --border-style: none;
  --background: transparent;
}

/* Layouts específicos */
.chat-track-layout.vertical .messages-container {
  gap: 12px;
}

.chat-track-layout.threaded .messages-container {
  gap: 8px;
}

.chat-track-layout.compact .messages-container {
  gap: 4px;
  padding: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .messages-container {
    padding: 12px 8px;
    gap: 8px;
  }
  
  .chat-track-layout.compact .messages-container {
    padding: 8px 4px;
    gap: 2px;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .messages-container {
    padding: 16px 12px;
  }
}

@media (min-width: 993px) {
  .messages-container {
    padding: 20px 16px;
    max-width: 800px;
    margin: 0 auto;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .chat-track-layout {
    background: var(--ion-background-color, #1a1a1a);
  }
  
  .chat-track-loading ion-spinner {
    --color: var(--ion-color-primary-tint);
  }
}

/* Animaciones */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-block-container {
  animation: messageSlideIn 0.3s ease-out;
}

/* Performance optimizations */
.chat-track-content {
  contain: layout style paint;
  will-change: scroll-position;
}

.message-block-container {
  contain: layout style;
}

/* Scrollbar styling */
.chat-track-content::-webkit-scrollbar {
  width: 6px;
}

.chat-track-content::-webkit-scrollbar-track {
  background: var(--ion-color-light);
}

.chat-track-content::-webkit-scrollbar-thumb {
  background: var(--ion-color-medium);
  border-radius: 3px;
}

.chat-track-content::-webkit-scrollbar-thumb:hover {
  background: var(--ion-color-medium-shade);
}
