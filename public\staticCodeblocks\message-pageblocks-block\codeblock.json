{"slug": "message-pageblocks-block", "id": "message-pageblocks-block", "name": "Message PageBlocks Block", "version": "1.0.0", "description": "Block individual para mensajes con contenido PageBlocks en chat track", "type": "static-codeblock", "category": "message-block", "tags": ["message", "pageblocks", "chat", "block", "recursive"], "author": "Proyector v1.2 Team", "created": "2025-01-22", "updated": "2025-01-22", "framework": "react", "location": "public", "metadata": {"framework": "React", "dependencies": ["@ionic/react", "framer-motion"], "responsive": true, "animated": true, "recursive": true, "performance": "optimized", "accessibility": "wcag-aa"}, "props": {"message": {"type": "object", "required": true, "description": "Objeto del mensaje con contenido PageBlocks"}, "isOwn": {"type": "boolean", "required": true, "description": "Si el mensaje es del usuario actual"}, "conversationId": {"type": "string", "required": true, "description": "ID de la conversación"}, "conversationType": {"type": "string", "required": true, "description": "Tipo de conversación"}, "pageblocks": {"type": "array", "required": true, "description": "Array de pageblocks a renderizar"}, "enableRecursive": {"type": "boolean", "default": true, "description": "Habilitar renderizado recursivo de pageblocks"}, "layout": {"type": "string", "default": "card", "enum": ["card", "inline", "expanded"], "description": "Layout del mensaje con pageblocks"}, "enableAnimations": {"type": "boolean", "default": true, "description": "Habilitar animaciones"}, "enableActions": {"type": "boolean", "default": true, "description": "Habilitar acciones del mensaje"}}, "controller": {"file": "component.jsx", "type": "react-controller", "exports": ["MessagePageBlocksBlockController"]}, "template": {"type": "pageblock", "renderer": "Universal<PERSON><PERSON><PERSON>", "layout": "message-pageblocks", "supports": {"recursivePageBlocks": true, "nestedLayouts": true, "dynamicContent": true}}, "styles": {"file": "styles.css", "type": "css", "scope": "component"}, "files": {"component": "./component.jsx", "config": "./codeblock.json", "styles": "./styles.css"}, "configuration": {"enableLazyLoading": true, "enableVirtualization": false, "cacheContent": true, "enableAnalytics": true, "recursiveDepth": 5}, "integrations": {"store": {"slice": "chatSlice", "actions": ["updateMessage", "deleteMessage", "reactToMessage"]}, "pageblocks": {"renderer": "Universal<PERSON><PERSON><PERSON>", "recursive": true, "maxDepth": 5}, "api": {"endpoints": ["/api/messages/{messageId}/pageblocks", "/api/messages/{messageId}/react"]}}, "permissions": {"view": ["message:view", "pageblocks:view"], "edit": ["message:edit", "pageblocks:edit"], "delete": ["message:delete"], "react": ["message:react"]}, "responsive": {"breakpoints": {"mobile": "max-width: 768px", "tablet": "max-width: 992px", "desktop": "min-width: 993px"}}}