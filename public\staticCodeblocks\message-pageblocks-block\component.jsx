/**
 * Message PageBlocks Block Controller
 * 
 * Block individual para mensajes con contenido PageBlocks en chat track.
 * Implementa renderizado recursivo de pageblocks dentro de mensajes.
 */

import React, { useState, useMemo, useCallback } from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonLabel,
  IonButton,
  IonChip,
  IonBadge,
  IonNote,
  IonAvatar,
  IonActionSheet,
  IonAccordion,
  IonAccordionGroup,
  IonItem,
  IonList,
  IonAlert
} from '@ionic/react';

// Icons
import {
  checkmarkDoneOutline,
  checkmarkOutline,
  timeOutline,
  alertCircleOutline,
  ellipsisVerticalOutline,
  heartOutline,
  heart,
  copyOutline,
  trashOutline,
  replyOutline,
  shareOutline,
  expandOutline,
  contractOutline,
  cubeOutline,
  layersOutline
} from 'ionicons/icons';

// Store hooks
import { useAuth } from '../../../src/proyector/store';

// UniversalRenderer para renderizado recursivo
import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';

// Motion
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Message PageBlocks Block Controller
 */
export function MessagePageBlocksBlockController({ 
  message,
  isOwn,
  conversationId,
  conversationType,
  pageblocks = [],
  enableRecursive = true,
  layout = 'card',
  enableAnimations = true,
  enableActions = true,
  className = ''
}) {
  // Store hooks
  const { user } = useAuth();

  // Estado local
  const [showActions, setShowActions] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAlert, setShowAlert] = useState(false);

  // Obtener pageblocks del mensaje
  const messagePageBlocks = useMemo(() => {
    return pageblocks || message.pageblocks || message.content?.pageblocks || [];
  }, [pageblocks, message]);

  // Configuración del remitente
  const senderConfig = useMemo(() => {
    if (isOwn) {
      return {
        name: user?.displayName || 'Tú',
        avatar: user?.photoURL,
        color: 'primary'
      };
    }

    if (message.senderId?.startsWith('bot-')) {
      return {
        name: message.senderName || 'Asistente IA',
        avatar: '/bot-avatar.png',
        color: 'secondary'
      };
    }

    return {
      name: message.senderName || 'Usuario',
      avatar: message.senderAvatar || '/default-avatar.png',
      color: 'tertiary'
    };
  }, [isOwn, user, message]);

  // Manejar acciones del mensaje
  const handleAction = useCallback((action) => {
    switch (action) {
      case 'like':
        setIsLiked(!isLiked);
        break;
      case 'copy':
        // Copiar contenido de pageblocks como JSON
        const content = JSON.stringify(messagePageBlocks, null, 2);
        navigator.clipboard.writeText(content);
        break;
      case 'expand':
        setIsExpanded(!isExpanded);
        break;
      case 'reply':
        // TODO: Implementar respuesta
        break;
      case 'delete':
        setShowAlert(true);
        break;
      case 'share':
        // TODO: Implementar compartir
        break;
    }
    setShowActions(false);
  }, [isLiked, isExpanded, messagePageBlocks]);

  // Confirmar eliminación
  const handleDelete = () => {
    // TODO: Implementar eliminación
    console.log('Eliminar mensaje:', message.id);
    setShowAlert(false);
  };

  // Obtener timestamp formateado
  const getFormattedTime = () => {
    if (!message.timestamp) return '';
    
    const date = new Date(message.timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Ahora';
    if (diffMins < 60) return `${diffMins}m`;
    return date.toLocaleTimeString();
  };

  // Renderizar pageblock individual
  const renderPageBlock = useCallback((pageblock, index) => {
    if (!enableRecursive) {
      return (
        <div key={index} className="pageblock-placeholder">
          <IonIcon icon={cubeOutline} />
          <span>PageBlock: {pageblock.type || 'unknown'}</span>
        </div>
      );
    }

    return (
      <motion.div
        key={index}
        className="pageblock-container"
        initial={enableAnimations ? { opacity: 0, y: 10 } : false}
        animate={enableAnimations ? { opacity: 1, y: 0 } : false}
        transition={enableAnimations ? { delay: index * 0.1 } : false}
      >
        <UniversalRenderer
          pageblock={pageblock}
          enableAnimations={enableAnimations}
          enableErrorBoundary={true}
          context={{
            messageId: message.id,
            conversationId,
            isInMessage: true
          }}
        />
      </motion.div>
    );
  }, [enableRecursive, enableAnimations, message.id, conversationId]);

  // Configuración de animación
  const animationConfig = enableAnimations ? {
    initial: { opacity: 0, scale: 0.95, y: 20 },
    animate: { opacity: 1, scale: 1, y: 0 },
    transition: { type: "spring", stiffness: 300, damping: 30 }
  } : {};

  return (
    <motion.div
      className={`message-pageblocks-block ${layout} ${isOwn ? 'own' : 'other'} ${className}`}
      {...animationConfig}
    >
      {layout === 'card' && (
        <IonCard className="message-pageblocks-card">
          <IonCardHeader>
            <div className="card-header">
              {/* Avatar y sender info */}
              <div className="sender-section">
                <IonAvatar size="small">
                  <img src={senderConfig.avatar} alt={senderConfig.name} />
                </IonAvatar>
                <div className="sender-info">
                  <IonLabel color={senderConfig.color}>
                    {senderConfig.name}
                  </IonLabel>
                  <IonNote>{getFormattedTime()}</IonNote>
                </div>
              </div>

              {/* Indicador de PageBlocks */}
              <IonChip color="tertiary" outline>
                <IonIcon icon={layersOutline} />
                <IonLabel>{messagePageBlocks.length} PageBlocks</IonLabel>
              </IonChip>

              {/* Acciones */}
              {enableActions && (
                <div className="header-actions">
                  <IonButton
                    fill="clear"
                    size="small"
                    onClick={() => handleAction('expand')}
                  >
                    <IonIcon icon={isExpanded ? contractOutline : expandOutline} />
                  </IonButton>
                  <IonButton
                    fill="clear"
                    size="small"
                    onClick={() => setShowActions(true)}
                  >
                    <IonIcon icon={ellipsisVerticalOutline} />
                  </IonButton>
                </div>
              )}
            </div>

            {/* Título del mensaje si existe */}
            {message.title && (
              <IonCardTitle>{message.title}</IonCardTitle>
            )}
          </IonCardHeader>

          <IonCardContent>
            {/* Descripción del mensaje si existe */}
            {message.description && (
              <div className="message-description">
                <p>{message.description}</p>
              </div>
            )}

            {/* PageBlocks Content */}
            <div className={`pageblocks-content ${isExpanded ? 'expanded' : 'collapsed'}`}>
              {isExpanded ? (
                // Vista expandida - todos los pageblocks
                <div className="pageblocks-expanded">
                  <AnimatePresence>
                    {messagePageBlocks.map((pageblock, index) => 
                      renderPageBlock(pageblock, index)
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                // Vista colapsada - preview o accordion
                <IonAccordionGroup>
                  <IonAccordion value="pageblocks-preview">
                    <IonItem slot="header">
                      <IonIcon icon={layersOutline} slot="start" />
                      <IonLabel>
                        <h3>Contenido PageBlocks</h3>
                        <p>{messagePageBlocks.length} elementos</p>
                      </IonLabel>
                    </IonItem>
                    <div slot="content" className="pageblocks-preview">
                      {messagePageBlocks.slice(0, 2).map((pageblock, index) => 
                        renderPageBlock(pageblock, index)
                      )}
                      {messagePageBlocks.length > 2 && (
                        <IonButton
                          fill="clear"
                          expand="block"
                          onClick={() => handleAction('expand')}
                        >
                          Ver {messagePageBlocks.length - 2} más...
                        </IonButton>
                      )}
                    </div>
                  </IonAccordion>
                </IonAccordionGroup>
              )}
            </div>

            {/* Footer con acciones */}
            <div className="message-footer">
              {enableActions && (
                <div className="footer-actions">
                  <IonButton
                    fill="clear"
                    size="small"
                    onClick={() => handleAction('like')}
                    className={`like-button ${isLiked ? 'liked' : ''}`}
                  >
                    <IonIcon icon={isLiked ? heart : heartOutline} />
                  </IonButton>
                </div>
              )}

              {/* Estado del mensaje */}
              {isOwn && message.status && (
                <IonBadge color="success" className="message-status">
                  <IonIcon icon={checkmarkDoneOutline} />
                </IonBadge>
              )}
            </div>
          </IonCardContent>
        </IonCard>
      )}

      {layout === 'inline' && (
        <div className="message-pageblocks-inline">
          <div className="inline-header">
            <IonChip color={senderConfig.color} outline>
              {senderConfig.name}
            </IonChip>
            <IonNote>{getFormattedTime()}</IonNote>
            <IonChip color="tertiary" outline>
              <IonIcon icon={layersOutline} />
              <IonLabel>{messagePageBlocks.length}</IonLabel>
            </IonChip>
          </div>
          <div className="inline-content">
            {messagePageBlocks.map((pageblock, index) => 
              renderPageBlock(pageblock, index)
            )}
          </div>
        </div>
      )}

      {layout === 'expanded' && (
        <div className="message-pageblocks-expanded">
          <div className="expanded-header">
            <div className="sender-section">
              <IonAvatar>
                <img src={senderConfig.avatar} alt={senderConfig.name} />
              </IonAvatar>
              <div className="sender-info">
                <IonLabel color={senderConfig.color}>
                  {senderConfig.name}
                </IonLabel>
                <IonNote>{getFormattedTime()}</IonNote>
              </div>
            </div>
          </div>
          <div className="expanded-content">
            {messagePageBlocks.map((pageblock, index) => 
              renderPageBlock(pageblock, index)
            )}
          </div>
        </div>
      )}

      {/* Action Sheet */}
      <IonActionSheet
        isOpen={showActions}
        onDidDismiss={() => setShowActions(false)}
        buttons={[
          {
            text: 'Copiar JSON',
            icon: copyOutline,
            handler: () => handleAction('copy')
          },
          {
            text: isExpanded ? 'Contraer' : 'Expandir',
            icon: isExpanded ? contractOutline : expandOutline,
            handler: () => handleAction('expand')
          },
          {
            text: 'Responder',
            icon: replyOutline,
            handler: () => handleAction('reply')
          },
          {
            text: 'Compartir',
            icon: shareOutline,
            handler: () => handleAction('share')
          },
          ...(isOwn ? [{
            text: 'Eliminar',
            icon: trashOutline,
            role: 'destructive',
            handler: () => handleAction('delete')
          }] : []),
          {
            text: 'Cancelar',
            role: 'cancel'
          }
        ]}
      />

      {/* Alert de confirmación */}
      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header="Eliminar mensaje"
        message="¿Estás seguro de que quieres eliminar este mensaje con PageBlocks?"
        buttons={[
          {
            text: 'Cancelar',
            role: 'cancel'
          },
          {
            text: 'Eliminar',
            role: 'destructive',
            handler: handleDelete
          }
        ]}
      />
    </motion.div>
  );
}

export default MessagePageBlocksBlockController;
