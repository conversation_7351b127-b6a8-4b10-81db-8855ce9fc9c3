{"slug": "message-text-block", "id": "message-text-block", "name": "Message Text Block", "version": "1.0.0", "description": "Block individual para mensajes de texto en chat track", "type": "static-codeblock", "category": "message-block", "tags": ["message", "text", "chat", "block"], "author": "Proyector v1.2 Team", "created": "2025-01-22", "updated": "2025-01-22", "framework": "react", "location": "public", "metadata": {"framework": "React", "dependencies": ["@ionic/react", "framer-motion"], "responsive": true, "animated": true, "performance": "optimized", "accessibility": "wcag-aa"}, "props": {"message": {"type": "object", "required": true, "description": "Objeto del mensaje con contenido y metadatos"}, "isOwn": {"type": "boolean", "required": true, "description": "Si el mensaje es del usuario actual"}, "conversationId": {"type": "string", "required": true, "description": "ID de la conversación"}, "conversationType": {"type": "string", "required": true, "description": "Tipo de conversación"}, "layout": {"type": "string", "default": "bubble", "enum": ["bubble", "card", "minimal"], "description": "Layout del mensaje"}, "enableAnimations": {"type": "boolean", "default": true, "description": "Habilitar animaciones"}, "enableMarkdown": {"type": "boolean", "default": true, "description": "Habilitar render<PERSON><PERSON>"}, "enableActions": {"type": "boolean", "default": true, "description": "Habilitar acciones del mensaje"}}, "controller": {"file": "component.jsx", "type": "react-controller", "exports": ["MessageTextBlockController"]}, "template": {"type": "pageblock", "renderer": "Universal<PERSON><PERSON><PERSON>", "layout": "message-bubble"}, "styles": {"file": "styles.css", "type": "css", "scope": "component"}, "files": {"component": "./component.jsx", "config": "./codeblock.json", "styles": "./styles.css"}, "configuration": {"enableLazyLoading": false, "enableVirtualization": false, "cacheContent": true, "enableAnalytics": true}, "integrations": {"store": {"slice": "chatSlice", "actions": ["updateMessage", "deleteMessage", "reactToMessage"]}, "api": {"endpoints": ["/api/messages/{messageId}/react", "/api/messages/{messageId}/delete"]}}, "permissions": {"view": ["message:view"], "edit": ["message:edit"], "delete": ["message:delete"], "react": ["message:react"]}, "responsive": {"breakpoints": {"mobile": "max-width: 768px", "tablet": "max-width: 992px", "desktop": "min-width: 993px"}}}