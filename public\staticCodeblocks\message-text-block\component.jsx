/**
 * Message Text Block Controller
 * 
 * Block individual para mensajes de texto en chat track.
 * Implementa la riqueza visual del legacy omnichat.
 */

import React, { useState, useMemo } from 'react';
import {
  IonCard,
  IonCardContent,
  IonIcon,
  IonLabel,
  IonButton,
  IonChip,
  IonBadge,
  IonNote,
  IonAvatar,
  IonActionSheet,
  IonPopover
} from '@ionic/react';

// Icons
import {
  checkmarkDoneOutline,
  checkmarkOutline,
  timeOutline,
  alertCircleOutline,
  ellipsisVerticalOutline,
  heartOutline,
  heart,
  copyOutline,
  trashOutline,
  replyOutline,
  shareOutline
} from 'ionicons/icons';

// Store hooks
import { useAuth } from '../../../src/proyector/store';

// Motion
import { motion } from 'framer-motion';

/**
 * Message Text Block Controller
 */
export function MessageTextBlockController({ 
  message,
  isOwn,
  conversationId,
  conversationType,
  layout = 'bubble',
  enableAnimations = true,
  enableMarkdown = true,
  enableActions = true,
  className = ''
}) {
  // Store hooks
  const { user } = useAuth();

  // Estado local
  const [showActions, setShowActions] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [showPopover, setShowPopover] = useState(false);

  // Configuración de estado del mensaje
  const statusConfig = useMemo(() => ({
    sending: {
      icon: timeOutline,
      color: 'medium',
      text: 'Enviando...'
    },
    delivered: {
      icon: checkmarkOutline,
      color: 'success',
      text: 'Entregado'
    },
    read: {
      icon: checkmarkDoneOutline,
      color: 'primary',
      text: 'Leído'
    },
    failed: {
      icon: alertCircleOutline,
      color: 'danger',
      text: 'Error'
    }
  }), []);

  // Obtener configuración del remitente
  const senderConfig = useMemo(() => {
    if (isOwn) {
      return {
        name: user?.displayName || 'Tú',
        avatar: user?.photoURL,
        color: 'primary'
      };
    }

    // Para bot agents o otros usuarios
    if (message.senderId?.startsWith('bot-')) {
      return {
        name: message.senderName || 'Asistente IA',
        avatar: '/bot-avatar.png',
        color: 'secondary'
      };
    }

    return {
      name: message.senderName || 'Usuario',
      avatar: message.senderAvatar || '/default-avatar.png',
      color: 'tertiary'
    };
  }, [isOwn, user, message]);

  // Procesar contenido con Markdown si está habilitado
  const processedContent = useMemo(() => {
    let content = message.content || '';

    if (enableMarkdown) {
      // Aplicar formato básico de Markdown
      content = content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
    }

    return content;
  }, [message.content, enableMarkdown]);

  // Manejar acciones del mensaje
  const handleAction = (action) => {
    switch (action) {
      case 'like':
        setIsLiked(!isLiked);
        // TODO: Implementar reacción en backend
        break;
      case 'copy':
        navigator.clipboard.writeText(message.content);
        break;
      case 'reply':
        // TODO: Implementar respuesta
        break;
      case 'delete':
        // TODO: Implementar eliminación
        break;
      case 'share':
        // TODO: Implementar compartir
        break;
    }
    setShowActions(false);
  };

  // Obtener timestamp formateado
  const getFormattedTime = () => {
    if (!message.timestamp) return '';
    
    const date = new Date(message.timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Ahora';
    if (diffMins < 60) return `${diffMins}m`;
    if (diffHours < 24) return `${diffHours}h`;
    if (diffDays < 7) return `${diffDays}d`;
    
    return date.toLocaleDateString();
  };

  // Configuración de animación
  const animationConfig = enableAnimations ? {
    initial: { opacity: 0, scale: 0.8, y: 20 },
    animate: { opacity: 1, scale: 1, y: 0 },
    transition: { type: "spring", stiffness: 300, damping: 30 }
  } : {};

  return (
    <motion.div
      className={`message-text-block ${layout} ${isOwn ? 'own' : 'other'} ${className}`}
      {...animationConfig}
    >
      {layout === 'bubble' && (
        <div className="message-bubble-container">
          {/* Avatar para mensajes de otros */}
          {!isOwn && (
            <IonAvatar className="message-avatar">
              <img src={senderConfig.avatar} alt={senderConfig.name} />
            </IonAvatar>
          )}

          {/* Bubble del mensaje */}
          <div className="message-bubble">
            {/* Header del mensaje */}
            {!isOwn && (
              <div className="message-header">
                <IonLabel className="sender-name" color={senderConfig.color}>
                  {senderConfig.name}
                </IonLabel>
                <IonNote className="message-time">
                  {getFormattedTime()}
                </IonNote>
              </div>
            )}

            {/* Contenido del mensaje */}
            <div 
              className="message-content"
              dangerouslySetInnerHTML={{ __html: processedContent }}
            />

            {/* Footer del mensaje */}
            <div className="message-footer">
              {/* Timestamp para mensajes propios */}
              {isOwn && (
                <IonNote className="message-time">
                  {getFormattedTime()}
                </IonNote>
              )}

              {/* Estado del mensaje */}
              {isOwn && message.status && statusConfig[message.status] && (
                <IonBadge 
                  color={statusConfig[message.status].color}
                  className="message-status"
                >
                  <IonIcon icon={statusConfig[message.status].icon} />
                </IonBadge>
              )}

              {/* Acciones del mensaje */}
              {enableActions && (
                <div className="message-actions">
                  {/* Botón de like */}
                  <IonButton
                    fill="clear"
                    size="small"
                    onClick={() => handleAction('like')}
                    className={`like-button ${isLiked ? 'liked' : ''}`}
                  >
                    <IonIcon icon={isLiked ? heart : heartOutline} />
                  </IonButton>

                  {/* Menú de acciones */}
                  <IonButton
                    fill="clear"
                    size="small"
                    onClick={() => setShowActions(true)}
                  >
                    <IonIcon icon={ellipsisVerticalOutline} />
                  </IonButton>
                </div>
              )}
            </div>
          </div>

          {/* Avatar para mensajes propios */}
          {isOwn && (
            <IonAvatar className="message-avatar">
              <img src={senderConfig.avatar} alt={senderConfig.name} />
            </IonAvatar>
          )}
        </div>
      )}

      {layout === 'card' && (
        <IonCard className="message-card">
          <IonCardContent>
            {/* Header con avatar y nombre */}
            <div className="card-header">
              <IonAvatar size="small">
                <img src={senderConfig.avatar} alt={senderConfig.name} />
              </IonAvatar>
              <div className="sender-info">
                <IonLabel color={senderConfig.color}>
                  {senderConfig.name}
                </IonLabel>
                <IonNote>{getFormattedTime()}</IonNote>
              </div>
              {enableActions && (
                <IonButton
                  fill="clear"
                  size="small"
                  onClick={() => setShowActions(true)}
                >
                  <IonIcon icon={ellipsisVerticalOutline} />
                </IonButton>
              )}
            </div>

            {/* Contenido */}
            <div 
              className="card-content"
              dangerouslySetInnerHTML={{ __html: processedContent }}
            />

            {/* Footer con estado */}
            {isOwn && message.status && (
              <div className="card-footer">
                <IonChip color={statusConfig[message.status].color}>
                  <IonIcon icon={statusConfig[message.status].icon} />
                  <IonLabel>{statusConfig[message.status].text}</IonLabel>
                </IonChip>
              </div>
            )}
          </IonCardContent>
        </IonCard>
      )}

      {layout === 'minimal' && (
        <div className="message-minimal">
          <div className="minimal-header">
            <IonChip color={senderConfig.color} outline>
              {senderConfig.name}
            </IonChip>
            <IonNote>{getFormattedTime()}</IonNote>
          </div>
          <div 
            className="minimal-content"
            dangerouslySetInnerHTML={{ __html: processedContent }}
          />
        </div>
      )}

      {/* Action Sheet */}
      <IonActionSheet
        isOpen={showActions}
        onDidDismiss={() => setShowActions(false)}
        buttons={[
          {
            text: 'Copiar',
            icon: copyOutline,
            handler: () => handleAction('copy')
          },
          {
            text: 'Responder',
            icon: replyOutline,
            handler: () => handleAction('reply')
          },
          {
            text: 'Compartir',
            icon: shareOutline,
            handler: () => handleAction('share')
          },
          ...(isOwn ? [{
            text: 'Eliminar',
            icon: trashOutline,
            role: 'destructive',
            handler: () => handleAction('delete')
          }] : []),
          {
            text: 'Cancelar',
            role: 'cancel'
          }
        ]}
      />
    </motion.div>
  );
}

export default MessageTextBlockController;
