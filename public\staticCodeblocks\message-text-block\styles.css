/**
 * Message Text Block Styles
 * 
 * Estilos para mensajes de texto con riqueza visual del legacy omnichat
 */

/* Container principal */
.message-text-block {
  width: 100%;
  margin-bottom: 8px;
}

/* Bubble Layout */
.message-text-block.bubble .message-bubble-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 85%;
}

.message-text-block.bubble.own .message-bubble-container {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-text-block.bubble.other .message-bubble-container {
  margin-right: auto;
  flex-direction: row;
}

/* Avatar */
.message-avatar {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Bubble del mensaje */
.message-bubble {
  background: var(--ion-color-light);
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-text-block.own .message-bubble {
  background: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  border-bottom-right-radius: 4px;
}

.message-text-block.other .message-bubble {
  background: var(--ion-color-light);
  color: var(--ion-color-dark);
  border-bottom-left-radius: 4px;
}

/* Header del mensaje */
.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.sender-name {
  font-size: 0.8rem;
  font-weight: 600;
  margin: 0;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
}

/* Contenido del mensaje */
.message-content {
  line-height: 1.4;
  margin: 4px 0;
}

.message-content p {
  margin: 0;
}

.message-content strong {
  font-weight: 600;
}

.message-content em {
  font-style: italic;
}

.message-content code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

/* Footer del mensaje */
.message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
  gap: 8px;
}

.message-status {
  --background: transparent;
  --color: currentColor;
  font-size: 0.7rem;
  padding: 2px 6px;
}

.message-status ion-icon {
  font-size: 0.8rem;
  margin-right: 2px;
}

/* Acciones del mensaje */
.message-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-text-block:hover .message-actions {
  opacity: 1;
}

.message-actions ion-button {
  --padding-start: 4px;
  --padding-end: 4px;
  --padding-top: 4px;
  --padding-bottom: 4px;
  height: 24px;
  width: 24px;
}

.like-button.liked {
  --color: var(--ion-color-danger);
}

/* Card Layout */
.message-text-block.card .message-card {
  margin: 8px 0;
  max-width: 90%;
}

.message-text-block.card.own .message-card {
  margin-left: auto;
  margin-right: 0;
}

.message-text-block.card.other .message-card {
  margin-left: 0;
  margin-right: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.sender-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sender-info ion-label {
  font-size: 0.9rem;
  font-weight: 600;
}

.sender-info ion-note {
  font-size: 0.7rem;
}

.card-content {
  line-height: 1.4;
  margin: 8px 0;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

/* Minimal Layout */
.message-text-block.minimal .message-minimal {
  padding: 8px 0;
  border-left: 3px solid var(--ion-color-medium);
  padding-left: 12px;
  margin: 4px 0;
}

.minimal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.minimal-content {
  line-height: 1.4;
  color: var(--ion-color-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .message-text-block.bubble .message-bubble-container {
    max-width: 95%;
  }
  
  .message-bubble {
    padding: 10px 14px;
    border-radius: 16px;
  }
  
  .message-avatar {
    width: 28px;
    height: 28px;
  }
  
  .message-text-block.card .message-card {
    max-width: 95%;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .message-text-block.bubble .message-bubble-container {
    max-width: 80%;
  }
}

@media (min-width: 993px) {
  .message-text-block.bubble .message-bubble-container {
    max-width: 70%;
  }
  
  .message-text-block.card .message-card {
    max-width: 75%;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .message-text-block.other .message-bubble {
    background: var(--ion-color-dark);
    color: var(--ion-color-light);
  }
  
  .message-content code {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .message-text-block.minimal .message-minimal {
    border-left-color: var(--ion-color-medium-tint);
  }
  
  .minimal-content {
    color: var(--ion-color-light);
  }
}

/* Animaciones */
@keyframes bubbleSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.message-text-block {
  animation: bubbleSlideIn 0.3s ease-out;
}

/* Estados de mensaje */
.message-status.sending {
  --color: var(--ion-color-medium);
}

.message-status.delivered {
  --color: var(--ion-color-success);
}

.message-status.read {
  --color: var(--ion-color-primary);
}

.message-status.failed {
  --color: var(--ion-color-danger);
}
