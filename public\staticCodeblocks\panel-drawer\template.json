{"component": "conditional", "condition": "{{controller.isOpen}}", "children": [{"component": "fragment", "children": [{"component": "conditional", "condition": "{{controller.settings.enableBackdrop}}", "children": [{"component": "div", "className": "panel-drawer__backdrop", "props": {"onClick": "{{!controller.persistent ? controller.closePanel : null}}"}}]}, {"component": "div", "className": "{{controller.getDrawerClasses()}}", "props": {"role": "dialog", "aria-modal": "true", "aria-labelledby": "panel-drawer-title"}, "children": [{"component": "IonHeader", "children": [{"component": "IonToolbar", "children": [{"component": "IonButtons", "props": {"slot": "start"}, "children": [{"component": "IonIcon", "props": {"icon": "{{controller.getModeIcon()}}", "className": "mr-2"}}]}, {"component": "IonTitle", "props": {"id": "panel-drawer-title"}, "children": [{"component": "text", "content": "{{controller.title || `Panel ${controller.mode || 'lateral'}`}}"}]}, {"component": "IonButtons", "props": {"slot": "end"}, "children": [{"component": "conditional", "condition": "{{controller.resizable}}", "children": [{"component": "IonButton", "props": {"fill": "clear", "onClick": "{{controller.minimizePanel}}", "aria-label": "Minimizar panel"}, "children": [{"component": "IonIcon", "props": {"icon": "removeOutline"}}]}]}, {"component": "conditional", "condition": "{{controller.dockable}}", "children": [{"component": "IonButton", "props": {"fill": "clear", "onClick": "{{() => controller.dockPanel(!controller.isDocked)}}", "aria-label": "Anclar/desanclar panel"}, "children": [{"component": "IonIcon", "props": {"icon": "{{controller.isDocked ? 'contractOutline' : 'expandOutline'}}"}}]}]}, {"component": "IonButton", "props": {"fill": "clear", "onClick": "{{controller.closePanel}}", "aria-label": "Cerrar panel"}, "children": [{"component": "IonIcon", "props": {"icon": "closeOutline"}}]}]}]}]}, {"component": "IonContent", "className": "panel-drawer__content", "children": [{"component": "div", "className": "panel-drawer__inner", "children": [{"component": "conditional", "condition": "{{controller.loading}}", "children": [{"component": "div", "className": "panel-drawer__loading", "children": [{"component": "Ion<PERSON><PERSON><PERSON>", "props": {"name": "crescent"}}, {"component": "text", "content": "Cargando contenido...", "className": "loading-text"}]}]}, {"component": "conditional", "condition": "{{controller.error}}", "children": [{"component": "IonCard", "props": {"color": "danger"}, "children": [{"component": "IonCardHeader", "children": [{"component": "IonCardTitle", "children": [{"component": "text", "content": "Error"}]}]}, {"component": "IonCardContent", "children": [{"component": "text", "content": "{{controller.error}}"}, {"component": "IonButton", "props": {"fill": "clear", "onClick": "{{() => controller.setState(prev => ({ ...prev, error: null }))}}", "className": "mt-2"}, "children": [{"component": "text", "content": "<PERSON><PERSON><PERSON>"}]}]}]}]}, {"component": "conditional", "condition": "{{!controller.loading && !controller.error}}", "children": [{"component": "switch", "condition": "{{controller.mode}}", "cases": {"filters": {"component": "filter-component", "props": {"entitySlug": "{{controller.entitySlug}}", "content": "{{controller.content}}", "onFiltersChange": "{{controller.onFilters<PERSON><PERSON><PERSON>}}", "onClose": "{{controller.closePanel}}"}}, "form": {"component": "form-component", "props": {"entitySlug": "{{controller.entitySlug}}", "docId": "{{controller.docId}}", "content": "{{controller.content}}", "mode": "{{controller.docId ? 'edit' : 'create'}}", "onSave": "{{controller.onSave}}", "onCancel": "{{controller.closePanel}}", "onClose": "{{controller.closePanel}}"}}, "details": {"component": "details-component", "props": {"entitySlug": "{{controller.entitySlug}}", "docId": "{{controller.docId}}", "content": "{{controller.content}}", "onEdit": "{{controller.onEdit}}", "onDelete": "{{controller.onDelete}}", "onClose": "{{controller.closePanel}}"}}, "navigation": {"component": "navigation-component", "props": {"entitySlug": "{{controller.entitySlug}}", "currentDocId": "{{controller.docId}}", "content": "{{controller.content}}", "onNavigate": "{{controller.onNavi<PERSON>}}", "onClose": "{{controller.closePanel}}"}}}, "default": {"component": "IonCard", "children": [{"component": "IonCardContent", "children": [{"component": "text", "content": "{{controller.content?.message || 'Contenido del panel'}}"}]}]}}]}]}]}]}]}]}