import { Route, Switch, Redirect } from "react-router-dom";
import { Toaster } from 'react-hot-toast';
import { IonApp, IonRouterOutlet, setupIonicReact } from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';

import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import utc from 'dayjs/plugin/utc';
import duration from "dayjs/plugin/duration";
import relativeTime from 'dayjs/plugin/relativeTime';
import "dayjs/locale/es";

// Proyector - Sistema Unificado
import { Proyector } from "./proyector/";
import { ProyectorHome } from "./proyector/pages";
import { ProyectorProvider } from "./proyector/store";

setupIonicReact();

dayjs.extend(isSameOrBefore)
dayjs.extend(utc);
dayjs.extend(localizedFormat)
dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.locale('es');

function App() {
  return (
    <ProyectorProvider>
      <div className="text-black h-screen flex">
        <IonApp>
          <IonReactRouter>
            <IonRouterOutlet>
              <Switch>
                {/* ✅ CORRECCIÓN: Proyector como sistema principal en root */}
                <Route path="/" render={props => (
                  <Proyector {...props} />
                )}/>

                {/* ❌ ELIMINADO: /proyector - ya no es necesario */}
                {/* ❌ ELIMINADO: /new - migrado a root / */}
              </Switch>
            </IonRouterOutlet>
          </IonReactRouter>
        </IonApp>
        <Toaster
          toastOptions={{
            duration: 8000,
            position: "bottom-center",
            style: {
              borderRadius: '10px',
              background: '#333',
              color: '#fff',
              fontFamily: 'Lato'
            }
          }}
        />
      </div>
    </ProyectorProvider>
  );
}

export default App;
