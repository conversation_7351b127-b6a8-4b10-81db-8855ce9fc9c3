import _ from 'lodash';
import {
  home,
  homeOutline,
  link,
  linkOutline,
  personCircleOutline,
  personCircle,
  sparkles,
  idCard,
  arrowBackCircle,
  sparklesOutline,
  addOutline,
  businessOutline,
  idCardOutline,
  cogOutline,
  documentTextOutline,
  cashOutline,
  receiptOutline,
  cubeOutline,
  documentsOutline,
  fileTrayStackedOutline,
  diamondOutline,
  createOutline,
  peopleOutline,
  logInOutline,
  keyOutline,
  cartOutline,
  pricetagsOutline,
  shapesOutline,
  gridOutline,
  newspaperOutline,
  extensionPuzzleOutline,
  arrowBackCircleOutline,
  chatbubbleEllipsesOutline,
  chatboxEllipsesOutline,
  layersOutline,
  refreshOutline,
  listOutline,
  codeOutline,
  codeSlashOutline,
  timeOutline,
  statsChartOutline,
  playCircleOutline,
  pauseCircleOutline,
  alertCircleOutline,
  checkmarkCircleOutline,
  gitNetworkOutline,
  analyticsOutline,
  archiveOutline,
  calendarOutline,
  listCircleOutline
} from 'ionicons/icons';
import { RxRulerSquare } from 'react-icons/rx';
import { PiHandshake } from "react-icons/pi";
import { RiRobot3Line } from "react-icons/ri";
import { getInstanceFromLocation, withPrefix } from './modules/instance/utilsInstance';
import { HiOutlineUserCircle, HiMiniUserCircle } from "react-icons/hi2";

// when creating new instances
export const modulesSlugsToInstall = [
  // sync with public\data\bundles.json
  'cart', 'blockStack', 'pagesTemplates',  // store bundle
  'sales', 'contacts', 'stock' // pos bundle
];
// to mark as installed
export const bundlesSlugsToInstall = ['base', 'store', 'pos', 'website'];

// when platform base install
export const mainBaseModules = ['panel', 'entity', 'user', 'sites', 'usersSections', 'blockStack', 'sales', 'contacts'];
export const mainBundlesSlugs = ['base', 'pos', 'store', 'website'];

// to extract contract plans
export const bundleSlugToExtractPlans = 'base';

// bundles to show on list
export const specBundles = [{
  slug: 'store',
  name: 'Tienda',
  // version: '1.0.0',
  description: 'Tienda virtual con páginas de catálogo y carrito de compras'
}, {
  slug: 'pos',
  name: 'Punto de venta',
  // version: '1.0.0',
  description: ' Punto de venta con facturación y gestión de créditos',
}, {
  slug: 'website',
  name: 'Sitio Web',
  // version: '1.0.0',
  description: 'Sitio web institucional con páginas y bloques de contenido',
}, {
  slug: 'base',
  name: 'Plataforma',
  // version: '1.0.0',
  description: 'Indispensables de la plataforma',
  needContract: true,
  // niveles de suscripción y sus planes de pago
  contractPlans: {
    'plan220': ['demo', 'monthly', 'yearly'],
    'plan165': ['demo', 'monthly', 'yearly'],
    'plan88': ['demo', 'monthly', 'yearly'],
    'plan44': ['demo', 'monthly', 'yearly'],
    'essential': ['free']
  }
}];

export const plansTitle = {
  essential: 'Gratuito',
  plan44: '44 mil',
  plan88: '88 mil',
  plan165: '165 mil',
  plan220: '220 mil'
};

export const plansOptions = [{
  label: 'Demo',
  value: 'demo',
}, {
  label: 'Mensual',
  value: 'monthly',
}, {
  label: 'Anual',
  value: 'yearly',
}];

export const planDetails = {
  essential: [
    <b>Gratuito</b>,
    "Sitio web de contenido base",
    <>Catálogo de <b>hasta 30 ítems</b> con <i>hasta 10 variaciones</i> por ítem</>,
    <>Facturación de <b>hasta 30 facturas</b> mensuales</>
  ],
  plan44: [
    <>Sitio web <b>institucional</b> con contenido extenso y personalizable</>,
    <>Catálogo de <b>hasta 100 ítems</b> con <i>hasta 15 variaciones</i> por ítem</>,
    <>Facturación de <b>hasta 100 facturas</b> mensuales</>
  ],
  plan88: [
    <>Sitio web <b>institucional</b> con contenido extenso y personalizable</>,
    <>Catálogo de <b>hasta 300 ítems</b> con <i>hasta 15 variaciones</i> por ítem</>,
    <>Facturación de <b>hasta 300 facturas</b> mensuales</>
  ],
  plan165: [
    <>Sitio web <b>institucional</b> con contenido extenso y personalizable</>,
    <>Catálogo de <b>hasta 1.500 ítems</b> con <i>hasta 25 variaciones</i> por ítem y <b>página personalizable</b></>,
    <>Facturación de <b>hasta 1.000 facturas</b> mensuales</>
  ],
  plan220: [
    <>Sitio web <b>institucional</b> con contenido extenso y personalizable</>,
    <>Catálogo de <b>hasta 3.000 ítems</b> con <i>hasta 30 variaciones</i> por ítem y <b>página personalizable</b></>,
    <>Facturación de <b>hasta 2.000 facturas</b> mensuales</>,
    <><b>Gestión de ventas a créditos</b></>,
    <b>Atención preferencial</b>
  ]
};

// used in entity creator form
const tabsOptions = [{
  value: 'usersSections',
  label: 'Sección de Usuario'
}, {
  value: 'entities',
  label: 'Listados'
}, {
  value: 'configs',
  label: 'Configuraciones'
}];

// used in sidebar
export const getMenuPages = ({ menuName, userAuth, selectedInstance, instance }) => {
  instance = instance || 'main';
  let homeLink = instance === 'main' ? '/' : `/${instance}`;
  let pages;

  if (menuName === 'top') {
    pages =  [
      ///// instance y panel fusionados
      {
        sectionKey: 'instance',
        title: 'Instancia', // selectedInstance?.data?.name
        ionIcon: linkOutline,
        ionIconAlt: link,
        permissions: { resource: 'usersSections', actions: ['tab:usersSections'] },
      },
      ///// panel individual
      // {
      //   sectionKey: 'panel',
      //   title: 'Panel',
      //   ionIcon: sparklesOutline,
      //   ionIconAlt: sparkles,
      //   permissions: { resource: 'panel', actions: ['tab:panel', 'instance:tab:panel'] } // base section
      // },
      ////// instance individual
      // {
      //   sectionKey: 'instance',
      //   title: 'Sitio', // selectedInstance?.data?.name
      //   ionIcon: linkOutline,
      //   ionIconAlt: link,
      //   permissions: { resource: 'usersSections', actions: ['tab:usersSections'] },
      // },
      {
        sectionKey: 'user',
        title: 'Usuario',
        faIcon: ({ className }) => (
          <HiOutlineUserCircle className={className} />
        ),
        faIconAlt: ({ className }) => (
          <HiMiniUserCircle className={className} />
        ),
        permissions: { resource: 'usersSections', actions: ['tab:usersSections'] }
      },
      {
        title: 'Portada',
        url: homeLink,
        ionIcon: arrowBackCircleOutline,
        ionIconAlt: arrowBackCircle,
        permissions: { resource: 'panel', actions: ['tab:panel', 'instance:tab:panel'] } // base section
      }
    ];
  }

  if (menuName === 'panel') {
    pages =  [
      // {
      //   title: 'Nuevo',
      //   url: `/a/panel`,
      //   ionIcon: addOutline,
      //   permissions: { resource: 'panel', actions: ['tab:panel', 'instance:tab:panel'] } // base section
      // },
      // {
      //   title: 'Episodio #',
      //   url: `/a/episode/#/docId/new`,
      //   ionIcon: addOutline,
      //   permissions: { resource: 'panel', actions: ['tab:panel', 'instance:tab:panel'] } // base section
      // }
    ];
  }

  if (menuName === 'user') {
    pages =  [
      {
        title: 'Mis Sites',
        url: '/a/usersSections/sites',
        ionIcon: businessOutline,
        permission: { resource: 'usersSections', action: 'tab:sites' },
      },
      {
        title: 'Datos personales',
        url: '/a/user/data',
        ionIcon: createOutline,
        permission: { resource: 'usersProfiles', action: 'owner:update' },
      },
      {
        title: 'Agentes Virtuales conectados',
        url: '/a/bots/connections',
        faIcon: ({ className }) => (
          <RiRobot3Line className={className} />
        ),
        permission: { resource: 'botUsers', action: 'read' },
        showInMenu: true,
        showInUserMenu: true
      },
      {
        title: 'Bot Agent Chat',
        url: '/a/botAgents/chat',
        ionIcon: chatbubbleEllipsesOutline,
        permission: { resource: 'botAgentChat', action: 'read' },
        showInMenu: true,
        showInUserMenu: true
      }
    ];
  }

  else if (menuName === 'instance') {
    pages =  [
      {
        title: 'Inicio',
        url: `/a/panel`,
        ionIcon: homeOutline,
        permissions: { resource: 'panel', actions: ['tab:panel', 'instance:tab:panel'] } // base section
      },
      {
        tabKey: 'store',
        title: 'Catálogo',
        ionIcon: gridOutline,
        permissions: { resource: 'panel', actions: ['tab:store', 'instance:tab:store'] },
        items: [
          {
            title: 'Pedidos',
            url: `/a/entity/${withPrefix(instance, 'carts')}/list`,
            permissions: { resource: withPrefix(instance, 'carts'), actions: ['section', 'instance:section'] },
            ionIcon: cartOutline
          },
          {
            title: 'Ítems',
            url: `/a/entity/${withPrefix(instance, 'cartItems')}/list`,
            permissions: { resource: withPrefix(instance, 'cartItems'), actions: ['section', 'instance:section'] },
            ionIcon: cubeOutline
          },
          {
            title: 'Categorías',
            url: `/a/entity/${withPrefix(instance, 'cartItemCategories')}/list`,
            permissions: { resource: withPrefix(instance, 'cartItemCategories'), actions: ['section', 'instance:section'] },
            ionIcon: pricetagsOutline
          },
          {
            title: 'Atributos de items',
            url: `/a/entity/${withPrefix(instance, 'cartItemFields')}/list`,
            permissions: { resource: withPrefix(instance, 'cartItemFields'), actions: ['section'] },
            ionIcon: shapesOutline
          }
        ]
      },
      {
        tabKey: 'sales',
        title: 'Ventas',
        faIcon: ({ className }) => (
          <PiHandshake className={`${className} !text-2xl`} />
        ),
        permissions: { resource: withPrefix(instance, 'invoices'), actions: ['tab:sales'] },
        items: [
          {
            title: 'Facturas',
            url: `/a/entity/${withPrefix(instance, 'invoices')}/list`,
            ionIcon: documentTextOutline,
            permission: { resource: withPrefix(instance, 'invoices'), action: 'section' },
          },
          {
            title: 'Cobros',
            url: `/a/entity/${withPrefix(instance, 'creditCollections')}/list`,
            ionIcon: cashOutline,
            permission: { resource: withPrefix(instance, 'creditCollections'), action: 'section' },
          },
          {
            title: 'Contrataciones',
            url: `/a/entity/${withPrefix(instance, 'contracts')}/list`,
            ionIcon: documentsOutline,
            permission: { resource: withPrefix(instance, 'contracts'), action: 'section' },
          },
          {
            title: 'Modelos de contratos',
            url: `/a/entity/${withPrefix(instance, 'contractsModels')}/list`,
            ionIcon: documentTextOutline,
            permission: { resource: withPrefix(instance, 'contractsModels'), action: 'section' },
          },
          {
            title: 'Talonarios de facturas',
            url: `/a/entity/${withPrefix(instance, 'invoiceBooks')}/list`,
            ionIcon: receiptOutline,
            permission: { resource: withPrefix(instance, 'invoiceBooks'), action: 'section' },
          },
          {
            title: 'Diseños para Impresión',
            url: `/a/entity/${withPrefix(instance, 'invoicesPrintsDesigns')}/list`,
            faIcon: RxRulerSquare,
            permission: { resource: withPrefix(instance, 'invoicesPrintsDesigns'), action: 'section' },
          },
        ]
      },
      {
        tabKey: 'stock',
        title: 'Control de Stock',
        ionIcon: archiveOutline,
        permissions: { resource: withPrefix(instance, 'stockBatches'), actions: ['tab:stock'] },
        items: [
          {
            title: 'Lotes de Cargamento',
            url: `/a/entity/${withPrefix(instance, 'stockBatches')}/list`,
            ionIcon: archiveOutline,
            permission: { resource: withPrefix(instance, 'stockBatches'), action: 'section' },
          },
          {
            title: 'Órdenes de Preventa',
            url: `/a/entity/${withPrefix(instance, 'presaleOrders')}/list`,
            ionIcon: calendarOutline,
            permission: { resource: withPrefix(instance, 'presaleOrders'), action: 'section' },
          },
          {
            title: 'Compras',
            url: `/a/entity/${withPrefix(instance, 'purchases')}/list`,
            ionIcon: receiptOutline,
            permission: { resource: withPrefix(instance, 'purchases'), action: 'section' },
          },
          {
            title: 'Alteraciones de Stock',
            url: `/a/entity/${withPrefix(instance, 'stockAlterations')}/list`,
            ionIcon: gitNetworkOutline,
            permission: { resource: withPrefix(instance, 'stockAlterations'), action: 'section' },
          }
        ]
      },
      {
        title: 'Contactos',
        url: `/a/entity/${withPrefix(instance, 'contacts')}/list`,
        ionIcon: peopleOutline,
        permissions: { resource: withPrefix(instance, 'contacts'), actions: ['tab:contacts'] }
      },
      {
        title: 'Estadísticas',
        url: '/a/stats/main',
        ionIcon: statsChartOutline,
        permissions: { resource: withPrefix(instance, 'stats'), actions: ['section'] },
      },
      {
        tabKey: 'entities',
        title: 'Listados',
        ionIcon: fileTrayStackedOutline,
        permissions: { resource: 'panel', actions: ['tab:lists', 'instance:tab:lists'] }
      },
      {
        tabKey: 'users',
        title: 'Accesos',
        ionIcon: logInOutline,
        permissions: { resource: 'panel', actions: ['tab:access', 'instance:tab:access'] },
        items: [
          {
            title: 'Usuarios',
            url: '/a/entity/usersProfiles/list',
            permissions: { resource: 'entities', actions: ['section', 'instance:section'] },
            ionIcon: peopleOutline
          },
          {
            title: 'Miembros',
            url: '/a/credentials/members',
            permissions: { resource: 'credentials', actions: ['members', 'instance:members'] },
            ionIcon: peopleOutline
          },
          {
            title: 'Credenciales',
            url: '/a/entity/credentials/list',
            permissions: { resource: 'entities', actions: ['section', 'instance:section'] },
            ionIcon: keyOutline
          },
          {
            title: 'Cargos',
            url: '/a/entity/roles/list',
            permissions: { resource: 'roles', actions: ['section', 'instance:section'] },
            ionIcon: keyOutline
          }
        ]
      },

      {
        tabKey: 'botAgents',
        title: 'Agentes Bot',
        faIcon: ({ className }) => (
          <RiRobot3Line className={className} />
        ),
        permissions: { resource: 'panel', actions: ['tab:botAgents', 'instance:tab:botAgents'] },
        items: [
          {
            title: 'Agentes',
            url: `/a/entity/${withPrefix(instance, 'botAgents')}/list`,
            permissions: { resource: withPrefix(instance, 'botAgents'), actions: ['section'] },
            faIcon: ({ className }) => (
              <RiRobot3Line className={className} />
            )
          },
          {
            title: 'Flujos de trabajo',
            url: `/a/entity/${withPrefix(instance, 'botFlows')}/list`,
            permissions: { resource: withPrefix(instance, 'botFlows'), actions: ['section'] },
            ionIcon: analyticsOutline
          },
          {
            title: 'Monitoreo',
            url: `/a/entity/${withPrefix(instance, 'botFlowExecutions')}/list`,
            permissions: { resource: withPrefix(instance, 'botFlowExecutions'), actions: ['section'] },
            ionIcon: statsChartOutline
          },
          {
            title: 'Configuraciones de Bots',
            url: `/a/entity/${withPrefix(instance, 'botConfigs')}/list`,
            permissions: { resource: withPrefix(instance, 'botConfigs'), actions: ['section', 'instance:section'] },
            ionIcon: chatboxEllipsesOutline
          },
        ]
      },

      {
        tabKey: 'configs',
        title: 'Configuraciones',
        ionIcon: cogOutline,
        permissions: { resource: 'panel', actions: ['tab:config', 'instance:tab:config'] },
        items: [
          {
            title: 'Páginas públicas',
            url: `/a/entity/${withPrefix(instance, 'pages')}/list`,
            permissions: { resource: withPrefix(instance, 'pages'), actions: ['section', 'instance:section'] },
            ionIcon: newspaperOutline
          },
          {
            title: 'Templates de páginas',
            url: `/a/entity/pagesTemplates/list`,
            permissions: { resource: 'pagesTemplates', actions: ['section'] },
            ionIcon: newspaperOutline
          },
          {
            title: 'Branding de Site',
            url: '/a/sitesBrands/update',
            permissions: { resource: 'sitesBrands', actions: ['update', 'site:update'] },
            ionIcon: diamondOutline
          },
          {
            title: 'Pagos del servicio',
            url: `/a/sitesBilling/update`,
            permissions: { resource: 'sitesBilling', actions: ['update', 'site:update'] },
            ionIcon: receiptOutline
          },
          {
            title: 'Paquetes de módulos',
            url: '/a/sitesModules/update',
            permission: { resource: 'sitesModules', action: ['update', 'site:update'] },
            ionIcon: extensionPuzzleOutline
          },
          {
            title: 'Módulos',
            url: `/a/entities/modules/#/instanceHash/${selectedInstance?.data?.hash}`,
            permissions: { resource: 'entities', actions: ['section'] },
            ionIcon: extensionPuzzleOutline
          },
          {
            title: 'General del Site',
            url: `/a/sites/config`,
            permissions: { resource: 'sites', actions: ['config', 'site:config'] },
            ionIcon: cogOutline
          },
          {
            title: 'Entidades',
            url: `/a/entityCreator/list/#/instanceHash/${selectedInstance?.data?.hash}`,
            permissions: { resource: 'entities', actions: ['section'] },
            ionIcon: cogOutline
          },
          {
            title: 'CodeBlocks',
            url: `/a/entity/${withPrefix(instance, 'codeBlocks')}/list`,
            permissions: { resource: withPrefix(instance, 'codeBlocks'), actions: ['section', 'instance:section'] },
            ionIcon: codeSlashOutline
          },
          {
            title: 'Migraciones',
            url: `/a/entities/migrations`,
            permissions: { resource: 'entities', actions: ['update'] },
            ionIcon: cogOutline
          },
          {
            title: 'Registrar nuevo site',
            url: `/a/usersSections/siteCreate`,
            permissions: { resource: 'sites', actions: ['config'] },
            ionIcon: addOutline
          },
        ]
      }
    ];
  }

  return pages;
};

// Configuración de emuladores según el puerto
const port = window.location.port;

// Configuración para cada grupo de emuladores
export const emulatorGroups = {
  // Grupo 1 (Default) - Puerto 3000 - Prefijo 11xx
  '3000': {
    groupName: 'Grupo 1 (Default)',
    auth: { port: 1101 },
    firestore: { port: 1110 },
    storage: { port: 1120 },
    functions: { port: 1130 },
    ui: { port: 1140 },
    hub: { port: 1150 },
    logging: { port: 1160 }
  },
  // Grupo 2 - Puerto 3001 - Prefijo 22xx
  '3001': {
    groupName: 'Grupo 2',
    auth: { port: 2201 },
    firestore: { port: 2210 },
    storage: { port: 2220 },
    functions: { port: 2230 },
    ui: { port: 2240 },
    hub: { port: 2250 },
    logging: { port: 2260 }
  },
  // Grupo 3 - Puerto 3002 - Prefijo 33xx
  '3002': {
    groupName: 'Grupo 3',
    auth: { port: 3301 },
    firestore: { port: 3310 },
    storage: { port: 3320 },
    functions: { port: 3330 },
    ui: { port: 3340 },
    hub: { port: 3350 },
    logging: { port: 3360 }
  }
};

// Obtener la configuración de emuladores según el puerto actual
let currentGroup = emulatorGroups[port] || emulatorGroups['3000'];

let config = {
  env: window.location.hostname === 'localhost' ? 'dev' : 'prod',
  platformName: `Tranqui Pro - ${currentGroup.groupName}`,
  platformImgPath: '/logo-platform/logo-black.svg',
  version: '1.0.0',
  prefixModels: false,
  currency: 'PYG',
  phoneCountry: 'py',
  localesNumberToTexts: ['ES_ES'],
  country: { countryId: 172, code: 'PYG', symbol: '₲', iso2: 'PY' },
  // whatsapp default message
  whatsAppTextBTN: function () { return `Hola ${this?.siteName}! me interesa saber más...` },
  whatsAppNewInstanceBTN: function () { return `Hola ${this?.siteName}! me interesa crear una nueva instancia` },
  // Información del grupo de emuladores actual
  emulatorGroup: currentGroup,

  /////////////////////////////////////////////////////
  //
  // BRAND
  //
  /////////////////////////////////////////////////////
  // assigned in last LayoutHome call

  /////////////////////////////////////////////////////
  //
  // PLATFORM
  //
  /////////////////////////////////////////////////////

  // reCaptcha
  reCaptchaSiteKey: "",
  // Maps
  googleMapApiKey: "AIzaSyDzzOk-fx4a_-Uw1sK5nlo735zFvn2h5wU",
  // Storage
  imgFolder: 'assetsImages',
  getImgPrefix: function (fileName, folder, instanceHash) {
    instanceHash = instanceHash || getInstanceFromLocation();
    if (window.location.hostname === 'localhost') {
      // Usar el puerto del emulador de Storage según el grupo actual
      const storagePort = currentGroup.storage.port;
      return `http://localhost:${storagePort}/v0/b/${config.projectSlug}.appspot.com/o/${instanceHash}%2F${folder || config.imgFolder}%2F${fileName}?alt=media`;
    }
    return `https://firebasestorage.googleapis.com/v0/b/${config.projectSlug}.appspot.com/o/${instanceHash}%2F${folder || config.imgFolder}%2F${fileName}?alt=media`;
  },
  // Links
  getURLprefix: function () { return `${this.protocol}://${this.domain}` },

  imageDimensions: [
    { width: 2500, height: 2500, suffix: '' },   // full
    { width: 1300, height: 1300, suffix: '-xl' }, // xl
    { width: 800, height: 800, suffix: '-md' },   // md
    { width: 300, height: 300, suffix: '-xs' },   // xs
  ],
  // Filter url
  urlSeparator: '---',

  /////////////////////////////////////////////////////
  //
  // Modules
  //
  /////////////////////////////////////////////////////
  modules: {
    panel: {
      mainPath: '/panel',
      configPath: '/config',
      panelSystemVersionEntitySlug: 'systemVersions',
      settingsEntitySlug: 'settings',
      gps: {
        defaultDistanceForInput: 5,
        defaultDistanceMin: 3,
        defaultDistanceMax: 25,
      },
      scrollYtoShowToTopBtn: 300,
      scrollYtoShowFixedNavbar: 300,
      tabsOptions
    },

    entity: {
      entitiesEntitySlug: 'entities',
      taxonomyTypesEntitySlug: 'taxonomyTypes',
      filterMenuEntitySlug: 'filterMenu'
    },

    user: {
      userSetDataPath: '/a/user/set-data',
      usersEntitySlug: "usersProfiles",
      rolesEntitySlug: "roles",
      credentialsEntitySlug: "credentials",
      userDefaultRoleSlug: "user",
      userDefaultRoleSlugForInstances: "customer",
      userTopRoleSlug: "super-admin",
      userTopRoleLabel: "Administrador",
      fieldCity: 'city'
    },

    delivery: {
      packagesEntitySlug: "packagesToSend",
      deliveryEntitySlug: "deliveryOrders",
    },

    publicProfile: {
      publicProfileEntitySlug: 'publicProfiles',
      conectionsEntitySlug: 'conections'
    },

    pages: {
      pagesEntitySlug: 'pages'
    },

    sites: {
      sitesEntitySlug: 'sites',
      sitesBrandsEntitySlug: 'sitesBrands',
      sitesColorsPalettesEntitySlug: 'sitesColorsPalettes',
      sitesLayoutBlocksEntitySlug: 'sitesLayoutBlocks',
      sitesMainPartsEntitySlug: 'sitesMainParts',
      sitesMainStringsEntitySlug: 'sitesMainStrings'
    },

    message: {
      conversationsEntitySlug: 'conversations',
      messagesEntitySlug: 'messages',
    },

    cart: {
      cartsEntitySlug: 'carts',
      cartItemEntitySlug: 'cartItems',
      cartItemVariantsEntitySlug: 'cartItemVariants',
      cartItemCategoriesEntitySlug: 'cartItemCategories',
      cartItemTypesEntitySlug: 'cartItemTypes',
      cartItemFieldsEntitySlug: 'cartItemFields',
    },

    sales: {
      contractsEntitySlug: 'contracts',
      contractsModelsEntitySlug: 'contractsModels',
      invoicesEntitySlug: 'invoices',
      paymentsEntitySlug: 'payments',
      invoiceBooksEntitySlug: 'invoiceBooks',
      creditCollectionsEntitySlug: 'creditCollections',
    },

    contacts: {
      contactsEntitySlug: 'contacts',
    },

    stock: {
      stockBatchesEntitySlug: 'stockBatches',
      presaleOrdersEntitySlug: 'presaleOrders',
      purchasesEntitySlug: 'purchases',
      stockAlterationsEntitySlug: 'stockAlterations'
    },

    canvas: {
      invoicesPrintsDesignsEntitySlug: 'invoicesPrintsDesigns'
    },

    botAgents: {
      botAgentsEntitySlug: 'botAgents',
      botConfigsEntitySlug: 'botConfigs'
    },

    botConnections: {
      botUsersEntitySlug: 'botUsers',
      botMessagesEntitySlug: 'botMessages',
      botConversationsEntitySlug: 'botConversations'
    },

    stats: {
      mainPath: '/a/stats',
      enabledModules: ['sales', 'contacts', 'cart', 'stock'],
      defaultTimeRange: '30d',
      refreshInterval: 300000, // 5 minutos en ms
      maxDataPoints: 1000,
      cacheTimeout: 60000, // 1 minuto en ms
      colors: {
        primary: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        info: '#06b6d4'
      }
    },

    // accountManager: {
    //   accountManagerEntitySlug: 'accountManagers'
    // },

    // verification: {
    //   verificationModeratorRoleSlug: "super-admin",
    //   types: {
    //     publicProfiles: {
    //       entitySlug: 'verificationsOfProfiles'
    //     }
    //   }
    // }
  },

  // LEGACY
  // youtube video tutorial embed id
  embedId: null,
  // Cart - whatsAppTextBTN ya está definido arriba, eliminando duplicado

  // Configuración de plantillas
  templates: {
    categories: [
      {
        id: "demos",
        name: "Demos",
        description: "Plantillas de demostración para propósitos de prueba y desarrollo"
      },
      {
        id: "clients",
        name: "Clientes",
        description: "Plantillas para clientes específicos"
      },
      {
        id: "main",
        name: "Oficiales",
        description: "Plantillas oficiales de la plataforma"
      }
    ]
  }
};

export const getConfig = () => {
  return config;
};

export const setConfig = (newConfig = {}) => {
  config = _.assign(config, newConfig);
};

export default config;