/**
 * MenuTop Styles - Sistema de Menú Top con 3 Modos
 * 
 * Estilos para el componente MenuTop que implementa:
 * - Modo Harmony: Chat con botAgents
 * - Modo Current Site: Menú lateral con secciones
 * - Modo User's Sections: Datos personales y configuración
 */

/* ===== CONTENEDOR PRINCIPAL ===== */
.menu-top {
  position: relative;
  z-index: 1000;
  background: var(--ion-background-color);
  border-bottom: 1px solid var(--ion-border-color);
  transition: all 0.3s ease;
}

.menu-top.collapsed {
  height: auto;
}

.menu-top.expanded {
  height: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== HEADER DEL MENU TOP ===== */
.menu-top-header {
  position: relative;
  z-index: 1001;
}

.menu-top-header ion-toolbar {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 60px;
}

/* ===== SEGMENT DE MODOS ===== */
.menu-top-segment {
  flex: 1;
  margin: 0 16px;
  --background: transparent;
  --color: var(--ion-text-color);
  --color-checked: var(--ion-color-primary);
  --border-radius: 8px;
}

.menu-top-segment ion-segment-button {
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  min-height: 44px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* ===== ICONOS Y LABELS DEL SEGMENT ===== */
.segment-icon {
  font-size: 20px;
  margin-bottom: 2px;
  transition: all 0.2s ease;
}

.segment-label {
  text-align: center;
  line-height: 1.2;
}

.mode-title {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 1px;
}

.mode-subtitle {
  font-size: 10px;
  opacity: 0.7;
  font-weight: 400;
}

/* Estados del segment */
ion-segment-button.segment-button-checked .segment-icon {
  transform: scale(1.1);
  color: var(--ion-color-primary);
}

ion-segment-button.segment-button-checked .mode-title {
  color: var(--ion-color-primary);
  font-weight: 700;
}

/* ===== CONTENIDO EXPANDIBLE ===== */
.menu-top-content {
  position: relative;
  background: var(--ion-background-color);
  border-top: 1px solid var(--ion-border-color);
  max-height: 400px;
  overflow-y: auto;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== CONTENIDO POR MODO ===== */

/* Modo Harmony */
.harmony-content {
  padding: 16px;
  background: linear-gradient(135deg, 
    var(--ion-color-primary-tint) 0%, 
    var(--ion-background-color) 100%);
  min-height: 300px;
}

.harmony-content .chat-system {
  background: var(--ion-background-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Modo Site */
.site-content {
  padding: 12px 16px;
  background: var(--ion-color-light);
}

.site-content .site-navigation {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.site-navigation-item {
  background: var(--ion-background-color);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid var(--ion-border-color);
  transition: all 0.2s ease;
  cursor: pointer;
}

.site-navigation-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--ion-color-primary);
}

/* Modo User */
.user-content {
  padding: 16px;
  background: linear-gradient(135deg, 
    var(--ion-color-tertiary-tint) 0%, 
    var(--ion-background-color) 100%);
}

.user-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
}

.user-section-card {
  background: var(--ion-background-color);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid var(--ion-border-color);
  transition: all 0.2s ease;
  cursor: pointer;
}

.user-section-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--ion-color-tertiary);
}

.user-section-icon {
  font-size: 32px;
  color: var(--ion-color-tertiary);
  margin-bottom: 8px;
}

.user-section-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--ion-text-color);
}

.user-section-description {
  font-size: 12px;
  opacity: 0.7;
  color: var(--ion-text-color);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet */
@media (max-width: 992px) {
  .menu-top-segment {
    margin: 0 8px;
  }
  
  .menu-top-segment ion-segment-button {
    --padding-start: 8px;
    --padding-end: 8px;
  }
  
  .mode-title {
    font-size: 11px;
  }
  
  .mode-subtitle {
    font-size: 9px;
  }
  
  .menu-top-content {
    max-height: 350px;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .menu-top-header ion-toolbar {
    --min-height: 50px;
    --padding-start: 8px;
    --padding-end: 8px;
  }
  
  .menu-top-segment {
    margin: 0 4px;
  }
  
  .menu-top-segment ion-segment-button {
    --padding-start: 6px;
    --padding-end: 6px;
    --padding-top: 6px;
    --padding-bottom: 6px;
    min-height: 38px;
  }
  
  .segment-icon {
    font-size: 18px;
  }
  
  .mode-title {
    font-size: 10px;
  }
  
  .mode-subtitle {
    display: none; /* Ocultar subtítulos en móvil */
  }
  
  .menu-top-content {
    max-height: 300px;
    padding: 8px;
  }
  
  .harmony-content,
  .user-content {
    padding: 12px;
  }
  
  .site-content {
    padding: 8px 12px;
  }
  
  .user-sections {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
  }
  
  .site-content .site-navigation {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* ===== DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  .menu-top {
    border-bottom-color: var(--ion-color-dark-shade);
  }
  
  .harmony-content {
    background: linear-gradient(135deg, 
      var(--ion-color-primary-shade) 0%, 
      var(--ion-background-color) 100%);
  }
  
  .user-content {
    background: linear-gradient(135deg, 
      var(--ion-color-tertiary-shade) 0%, 
      var(--ion-background-color) 100%);
  }
  
  .site-content {
    background: var(--ion-color-dark);
  }
}

/* ===== ANIMACIONES ADICIONALES ===== */
.menu-top-segment ion-segment-button {
  transition: all 0.2s ease;
}

.menu-top-segment ion-segment-button:hover {
  transform: translateY(-1px);
}

.menu-top ion-chip {
  transition: all 0.2s ease;
}

.menu-top ion-chip:hover {
  transform: scale(1.05);
}
