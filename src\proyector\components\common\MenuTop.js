/**
 * MenuTop Component - Sistema de Menú Top con 3 Modos
 * 
 * Implementa el sistema de menú top especificado en next-steps.md:
 * - Modo Harmony: Chat con botAgents
 * - Modo Current Site: Menú lateral con secciones del site actual
 * - Modo User's Sections: Datos personales, mis sites, configuraciones
 */

import React, { useState, useEffect } from 'react';
import {
  IonHeader,
  IonToolbar,
  IonButtons,
  IonButton,
  IonIcon,
  IonTitle,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonChip,
  IonNote
} from '@ionic/react';

// Icons
import {
  chatbubbles,
  chatbubblesOutline,
  layers,
  layersOutline,
  person,
  personOutline,
  menuOutline,
  closeOutline
} from 'ionicons/icons';

// Store hooks
import { useUI, useAuth, useSite } from '../../store';

// Components
import { UniversalRenderer } from '../universal/UniversalRenderer';

/**
 * MenuTop Component
 */
export function MenuTop({ history, className = '' }) {
  // Store hooks
  const { layout, setMenuTopMode, setChatVisible, setSidebarCollapsed } = useUI();
  const { user, isAuthenticated } = useAuth();
  const { current: currentSite } = useSite();

  // Estado local del modo actual
  const [currentMode, setCurrentMode] = useState(layout.menuTopMode || 'site');
  const [isExpanded, setIsExpanded] = useState(false);

  // Configuración de modos
  const modes = {
    harmony: {
      id: 'harmony',
      title: 'Harmony',
      subtitle: 'Chat con botAgents',
      icon: chatbubblesOutline,
      iconActive: chatbubbles,
      color: 'primary',
      hidesSideMenu: true,
      component: 'chat-system',
      description: 'Asistente inteligente con herramientas'
    },
    site: {
      id: 'site',
      title: currentSite?.displayName || currentSite?.data?.name || 'Main Site',
      subtitle: 'Navegación del site',
      icon: layersOutline,
      iconActive: layers,
      color: 'secondary',
      showsSideMenu: true,
      component: 'site-navigation',
      description: 'Secciones y módulos del site'
    },
    user: {
      id: 'user',
      title: user?.displayName || user?.email || 'Usuario',
      subtitle: 'Perfil y configuración',
      icon: personOutline,
      iconActive: person,
      color: 'tertiary',
      hidesSideMenu: true,
      component: 'user-sections',
      description: 'Datos personales y configuraciones'
    }
  };

  // Efecto para sincronizar con el store
  useEffect(() => {
    if (layout.menuTopMode !== currentMode) {
      setCurrentMode(layout.menuTopMode || 'site');
    }
  }, [layout.menuTopMode]);

  // Manejar cambio de modo
  const handleModeChange = (modeId) => {
    const mode = modes[modeId];
    if (!mode) return;

    // Actualizar estado local
    setCurrentMode(modeId);
    
    // Actualizar store
    setMenuTopMode(modeId);

    // Manejar visibilidad del sidebar según el modo
    if (mode.hidesSideMenu) {
      setSidebarCollapsed(true);
    } else if (mode.showsSideMenu) {
      setSidebarCollapsed(false);
    }

    // Manejar chat específicamente para modo Harmony
    if (modeId === 'harmony') {
      setChatVisible(true);
      setIsExpanded(true);
    } else {
      // En otros modos, mantener estado actual del chat
      setIsExpanded(false);
    }
  };

  // Toggle expansión del menú top
  const handleToggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Obtener modo actual
  const activeMode = modes[currentMode];

  return (
    <div className={`menu-top ${isExpanded ? 'expanded' : 'collapsed'} ${className}`}>
      {/* Header del MenuTop */}
      <IonHeader className="menu-top-header">
        <IonToolbar color={activeMode.color}>
          {/* Selector de modos */}
          <IonSegment
            value={currentMode}
            onIonChange={(e) => handleModeChange(e.detail.value)}
            className="menu-top-segment"
          >
            {Object.values(modes).map((mode) => (
              <IonSegmentButton key={mode.id} value={mode.id}>
                <IonIcon 
                  icon={currentMode === mode.id ? mode.iconActive : mode.icon}
                  className="segment-icon"
                />
                <IonLabel className="segment-label">
                  <div className="mode-title">{mode.title}</div>
                  <div className="mode-subtitle">{mode.subtitle}</div>
                </IonLabel>
              </IonSegmentButton>
            ))}
          </IonSegment>

          {/* Controles del header */}
          <IonButtons slot="end">
            {/* Información del modo actual */}
            <IonChip color={activeMode.color} outline>
              <IonIcon icon={activeMode.iconActive} />
              <IonLabel>{activeMode.description}</IonLabel>
            </IonChip>

            {/* Toggle expansión */}
            <IonButton
              fill="clear"
              onClick={handleToggleExpansion}
              title={isExpanded ? 'Contraer menú' : 'Expandir menú'}
            >
              <IonIcon icon={isExpanded ? closeOutline : menuOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      {/* Contenido expandible del MenuTop */}
      {isExpanded && (
        <div className="menu-top-content">
          {/* Renderizar componente según modo activo */}
          <MenuTopContent 
            mode={activeMode}
            currentSite={currentSite}
            user={user}
            history={history}
            onNavigate={() => setIsExpanded(false)}
          />
        </div>
      )}
    </div>
  );
}

/**
 * Contenido del MenuTop según el modo
 */
function MenuTopContent({ mode, currentSite, user, history, onNavigate }) {
  switch (mode.id) {
    case 'harmony':
      return (
        <div className="harmony-content">
          <UniversalRenderer
            blockType="chat-system"
            config={{
              mode: 'harmony',
              enableBotAgents: true,
              enableTools: true,
              siteId: currentSite?.siteId,
              userId: user?.uid,
              layout: 'expanded'
            }}
            enableAnimations={true}
            enableErrorBoundary={true}
          />
        </div>
      );

    case 'site':
      return (
        <div className="site-content">
          <UniversalRenderer
            blockType="site-navigation"
            config={{
              siteId: currentSite?.siteId,
              navigation: currentSite?.config?.navigation,
              layout: 'horizontal',
              showModules: true,
              showPages: true
            }}
            enableAnimations={true}
            enableErrorBoundary={true}
          />
        </div>
      );

    case 'user':
      return (
        <div className="user-content">
          <UniversalRenderer
            blockType="user-sections"
            config={{
              userId: user?.uid,
              sections: ['profile', 'sites', 'settings', 'preferences'],
              layout: 'grid',
              enableQuickActions: true
            }}
            enableAnimations={true}
            enableErrorBoundary={true}
          />
        </div>
      );

    default:
      return (
        <div className="default-content">
          <IonNote>Modo no reconocido: {mode.id}</IonNote>
        </div>
      );
  }
}

export default MenuTop;
