/**
 * Proyector - Sistema Unificado de Estado y Navegación
 * 
 * Punto de entrada principal del proyector que implementa:
 * - Store unificado basado en Zustand
 * - Sistema de routing independiente
 * - Compatibilidad total con backend existente
 */

import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { IonRouterOutlet } from '@ionic/react';

// Componentes de routing
import { ProyectorRouter } from './routes';

// Layouts
import { ProyectorLayout } from './components/layouts/ProyectorLayout';

/**
 * Componente principal del Proyector
 * ✅ CORRECCIÓN: Maneja el routing en root / y proporciona el store unificado
 */
export function Proyector(props) {
  const { history } = props;

  // Exponer history globalmente para navegación desde controllers
  React.useEffect(() => {
    window.routerHistory = history;
    return () => {
      delete window.routerHistory;
    };
  }, [history]);

  return (
    <ProyectorLayout history={history}>
        <IonRouterOutlet>
          <Switch>
            {/* ✅ CORRECCIÓN: Portada pública del main site en root */}
            <Route
              path="/"
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="main/home"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Portada específica de site */}
            <Route
              path="/:siteSlug"
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="sites/site-info"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Panel principal (site main) */}
            <Route
              path="/panel"
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="panel/panel"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Panel de site específico */}
            <Route
              path="/:siteSlug/panel"
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="panel/panel"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Entidades - Lista */}
            <Route
              path={["/entity/:entitySlug/list", "/:siteSlug/entity/:entitySlug/list"]}
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="entity-list"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Entidades - Formulario */}
            <Route
              path={["/entity/:entitySlug/:docId/form", "/:siteSlug/entity/:entitySlug/:docId/form"]}
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="entity-form"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Entidades - Vista */}
            <Route
              path={["/entity/:entitySlug/:docId", "/:siteSlug/entity/:entitySlug/:docId"]}
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="entity-show"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Rutas de secciones con subpáginas */}
            <Route
              path={["/:sectionSlug/:subPage", "/:siteSlug/:sectionSlug/:subPage"]}
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="sidebar-navigation"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Rutas de secciones (sin subpágina específica) */}
            <Route
              path={["/:sectionSlug", "/:siteSlug/:sectionSlug"]}
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="sidebar-navigation"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Rutas de módulos personalizados */}
            <Route
              path={["/:entitySlug/:action", "/:siteSlug/:entitySlug/:action"]}
              exact
              render={(routeProps) => (
                <ProyectorRouter
                  {...routeProps}
                  route="module-page"
                  history={history}
                />
              )}
            />

            {/* ✅ CORRECCIÓN: Ruta por defecto - redirigir al panel */}
            <Route
              path={["/:siteSlug"]}
              exact
              render={(routeProps) => {
                const siteSlug = routeProps.match.params.siteSlug;
                // Para site main, usar /panel, para otros sites usar /:siteSlug/panel
                const panelUrl = (siteSlug && siteSlug !== 'main') ?
                  `/${siteSlug}/panel` :
                  '/panel';
                history.push(panelUrl);
                return null;
              }}
            />
          </Switch>
        </IonRouterOutlet>
      </ProyectorLayout>
  );
}

export default Proyector;