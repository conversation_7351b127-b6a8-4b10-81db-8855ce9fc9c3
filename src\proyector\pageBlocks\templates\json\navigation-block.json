{"blockType": "NavigationBlock", "version": "1.2", "description": "Navegación universal con menús dinámicos y breadcrumbs", "template": {"component": "nav", "props": {"className": "universal-navigation {{config.variant || 'default'}}"}, "children": [{"component": "div", "props": {"className": "navigation-breadcrumbs"}, "conditional": "{{config.showBreadcrumbs && data.breadcrumbs}}", "children": [{"component": "ion-chip", "props": {"className": "breadcrumb-item {{item.active ? 'active' : ''}}"}, "children": [{"component": "ion-icon", "props": {"icon": "{{item.icon}}", "slot": "start"}, "conditional": "{{item.icon}}"}, {"component": "ion-label", "children": ["{{item.label}}"]}], "repeat": "{{data.breadcrumbs}}", "repeatVar": "item"}]}, {"component": "ion-list", "props": {"className": "navigation-menu"}, "children": [{"component": "ion-item", "props": {"button": true, "onClick": "{{controller.executeAction}}", "data-action": "navigate", "data-target": "{{item.path}}", "className": "nav-item {{item.active ? 'active' : ''}} {{item.disabled ? 'disabled' : ''}}"}, "conditional": "{{!item.hidden}}", "children": [{"component": "ion-icon", "props": {"icon": "{{item.icon}}", "slot": "start", "color": "{{item.active ? 'primary' : 'medium'}}"}, "conditional": "{{item.icon}}"}, {"component": "ion-label", "children": [{"component": "h3", "children": ["{{item.label}}"]}, {"component": "p", "children": ["{{item.description}}"], "conditional": "{{item.description}}"}]}, {"component": "ion-badge", "props": {"color": "{{item.badge.color || 'primary'}}", "slot": "end"}, "children": ["{{item.badge.text}}"], "conditional": "{{item.badge}}"}, {"component": "ion-icon", "props": {"icon": "chevronForward", "slot": "end", "color": "medium"}, "conditional": "{{config.showArrows}}"}], "repeat": "{{data.menuItems}}", "repeatVar": "item"}]}]}, "dataSources": {"menuItems": {"type": "firebase", "path": "sites/{{siteId}}/navigation/menu", "realTime": true, "transform": "processMenuItems"}, "breadcrumbs": {"type": "computed", "compute": "generateBreadcrumbs"}, "userPermissions": {"type": "firebase", "path": "sites/{{siteId}}/permissions/{{userId}}", "realTime": true}}, "controller": {"name": "NavigationController", "actions": {"navigate": {"type": "navigation", "handler": "handleNavigation"}, "toggleSubmenu": {"type": "ui", "handler": "toggleSubmenu"}}, "methods": {"processMenuItems": "filterByPermissions", "generateBreadcrumbs": "buildBreadcrumbsFromRoute", "handleNavigation": "navigateWithAnalytics"}, "events": {"onMount": "loadNavigationData", "onRouteChange": "updateActiveItems"}}, "variants": {"sidebar": {"showBreadcrumbs": false, "showArrows": true, "className": "navigation-sidebar"}, "horizontal": {"showBreadcrumbs": true, "showArrows": false, "className": "navigation-horizontal"}, "tabs": {"showBreadcrumbs": false, "showArrows": false, "className": "navigation-tabs"}, "minimal": {"showBreadcrumbs": false, "showArrows": false, "className": "navigation-minimal"}}, "styling": {"css": {".universal-navigation": {"width": "100%"}, ".navigation-breadcrumbs": {"padding": "8px 16px", "display": "flex", "gap": "4px", "flex-wrap": "wrap"}, ".breadcrumb-item": {"font-size": "0.8rem"}, ".breadcrumb-item.active": {"background": "var(--ion-color-primary)", "color": "var(--ion-color-primary-contrast)"}, ".navigation-menu": {"padding": 0}, ".nav-item.active": {"background": "var(--ion-color-primary-tint)", "border-left": "3px solid var(--ion-color-primary)"}, ".nav-item.disabled": {"opacity": 0.5, "pointer-events": "none"}, ".navigation-sidebar": {"height": "100%", "border-right": "1px solid var(--ion-color-light-shade)"}, ".navigation-horizontal .navigation-menu": {"display": "flex", "overflow-x": "auto"}, ".navigation-tabs .nav-item": {"text-align": "center", "min-width": "80px"}}}, "animations": {"enter": {"initial": {"opacity": 0, "x": -20}, "animate": {"opacity": 1, "x": 0}, "transition": {"duration": 0.3, "staggerChildren": 0.1}}, "itemHover": {"whileHover": {"scale": 1.02, "x": 4}, "transition": {"duration": 0.2}}}}