/**
 * Sistema de Routing del Proyector
 * 
 * Maneja el routing centralizado con:
 * - Resolución de rutas por tipo
 * - Integración con store unificado
 * - Carga de datos por ruta
 * - Navegación programática
 */

import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

// Store
import { useSite, useUI, useSystem, useProyectorStore } from '../store';

// PageBlocks System
import { PageTypeRenderer } from '../pageBlocks/PageBlockRenderer';

// Componente de loading
import { LoadingPage } from '../components/common/LoadingPage';
import SystemInitializationScreen from '../components/system/SystemInitializationScreen';

/**
 * Router principal del proyector
 * Resuelve rutas y carga componentes apropiados
 */
export function ProyectorRouter(props) {
  const { route, match } = props;
  const location = useLocation();

  // Store hooks
  const { selectFromUrl, available: sites, isLoading: sitesLoading } = useSite();
  const { setCurrentRoute, setNavigationLoading } = useUI();

  // ✅ CORRECCIÓN: Usar systemSlice en lugar de hook legacy
  const {
    status: systemStatus,
    needsInitialization,
    isSystemReady,
    hasError,
    checkSystemStatus
  } = useSystem();

  // Estado local
  const [isInitialized, setIsInitialized] = useState(false);
  const [showInitializationScreen, setShowInitializationScreen] = useState(false);

  // ✅ ARQUITECTURA ZUSTAND: La lógica de inicialización está en el store
  // ProyectorRouter solo maneja renders basado en el estado de Zustand

  // ✅ CORRECCIÓN: Inicialización automática del sistema
  useEffect(() => {
    if (!systemStatus.loading && !systemStatus.initialized && !systemStatus.mainSiteExists) {
      console.log('🔄 [ProyectorRouter] Iniciando verificación del sistema...');
      checkSystemStatus();
    }
  }, []); // Solo al montar

  // Verificar si necesita mostrar pantalla de inicialización
  useEffect(() => {
    if (!systemStatus.loading) {
      // ✅ CORRECCIÓN: Mostrar pantalla de inicialización también cuando firstUserAssigned: false
      const needsFirstUserAssignment = systemStatus.initialized &&
                                      systemStatus.mainSiteExists &&
                                      !systemStatus.firstUserAssigned;

      console.log('🔍 [ProyectorRouter] Estado del sistema normalizado:', {
        initialized: systemStatus.initialized,
        mainSiteExists: systemStatus.mainSiteExists,
        firstUserAssigned: systemStatus.firstUserAssigned,
        needsInitialization: needsInitialization(),
        needsFirstUserAssignment,
        hasError,
        route,
        loading: systemStatus.loading
      });

      if ((needsInitialization() || needsFirstUserAssignment) && !hasError) {
        console.log('✅ Mostrando pantalla de inicialización');
        setShowInitializationScreen(true);
      } else if (isSystemReady && systemStatus.firstUserAssigned) {
        console.log('✅ Sistema listo, ocultando pantalla de inicialización');
        setShowInitializationScreen(false);
      }
    }
  }, [systemStatus.loading, systemStatus.firstUserAssigned, needsInitialization, isSystemReady, hasError, route]);

  // ✅ CORRECCIÓN: Inicializar ruta cuando el sistema esté listo
  useEffect(() => {
    // Inicializar si el sistema está listo, independientemente de sites
    if (isSystemReady && !sitesLoading && !showInitializationScreen) {
      console.log('🔄 [ProyectorRouter] Sistema listo, inicializando ruta...', {
        isSystemReady,
        sitesLoading,
        sitesCount: sites?.length || 0,
        showInitializationScreen
      });
      initializeRoute();
    }
  }, [location.pathname, route, sitesLoading, isSystemReady, showInitializationScreen]);

  /**
   * ✅ CORRECCIÓN: Inicializar la ruta actual
   */
  const initializeRoute = async () => {
    try {
      setNavigationLoading(true);

      // ✅ CORRECCIÓN: Seleccionar site desde URL si está disponible, sino usar main por defecto
      if (sites && sites.length > 0) {
        selectFromUrl(location.pathname);
      } else {
        console.log('🔄 [ProyectorRouter] Sites no disponibles, usando configuración por defecto');
      }

      // Extraer parámetros de la ruta
      const routeParams = extractRouteParams(match, route);

      // Establecer ruta actual en el store
      setCurrentRoute(route, routeParams);

      console.log('✅ [ProyectorRouter] Ruta inicializada correctamente:', {
        route,
        routeParams,
        pathname: location.pathname
      });

      setIsInitialized(true);
    } catch (error) {
      console.error('❌ [ProyectorRouter] Error inicializando ruta:', error);
      // ✅ CORRECCIÓN: Aún así marcar como inicializado para evitar loops
      setIsInitialized(true);
    } finally {
      setNavigationLoading(false);
    }
  };

  /**
   * Extraer parámetros de la ruta
   */
  const extractRouteParams = (match, routeType) => {
    const params = { ...match.params };

    // Agregar parámetros específicos por tipo de ruta
    switch (routeType) {
      case 'entity-list':
      case 'entity-form':
      case 'entity-show':
        params.entitySlug = match.params.entitySlug;
        if (match.params.docId) {
          params.docId = match.params.docId;
        }
        break;

      case 'module-page':
        params.entitySlug = match.params.entitySlug;
        params.action = match.params.action;
        break;

      case 'sites/site-info':
        // Para páginas de información de site, pasar el instance como siteHash
        if (match.params.instance) {
          params.siteHash = match.params.instance;
        }
        break;
    }

    return params;
  };



  /**
   * Manejar completado de inicialización
   */
  const handleInitializationComplete = () => {
    console.log('🎉 [ProyectorRouter] Inicialización completada, actualizando estado...');
    setShowInitializationScreen(false);

    // ✅ CORRECCIÓN: En lugar de reload, forzar recarga de datos
    // Esto evita loops infinitos y es más eficiente
    checkSystemStatus(true); // Forzar refresh del estado del sistema

    // Forzar re-inicialización de la ruta
    setIsInitialized(false);
    setTimeout(() => {
      setIsInitialized(true);
    }, 100);
  };

  /**
   * Renderizar componente apropiado
   */
  // Mostrar pantalla de inicialización si es necesario
  if (showInitializationScreen) {
    return (
      <SystemInitializationScreen
        onInitializationComplete={handleInitializationComplete}
      />
    );
  }

  // Mostrar loading mientras se verifica el sistema o se cargan los sites
  if (systemStatus.loading || sitesLoading || !isInitialized) {
    const loadingReason = systemStatus.loading ? 'sistema' :
                         sitesLoading ? 'sites' :
                         !isInitialized ? 'ruta' : 'desconocido';

    console.log(`🔄 [ProyectorRouter] Mostrando loading por: ${loadingReason}`, {
      systemLoading: systemStatus.loading,
      sitesLoading,
      isInitialized,
      systemStatus: systemStatus.initialized,
      mainSiteExists: systemStatus.mainSiteExists,
      firstUserAssigned: systemStatus.firstUserAssigned
    });

    return <LoadingPage message={`Cargando ${loadingReason}...`} />;
  }

  // Todas las rutas ahora usan PageBlockRenderer

  // Extraer parámetros específicos para el tipo de ruta
  const routeParams = extractRouteParams(match, route);

  // Usar PageTypeRenderer para el resto
  return (
    <PageTypeRenderer
      pageType={route}
      params={routeParams}
      className="proyector-page"
    />
  );
}

/**
 * Hook para navegación programática
 */
export const useProyectorNavigation = () => {
  const { getUrlWithPrefix } = useSite();
  
  return {
    // ✅ CORRECCIÓN: Navegar a panel
    goToPanel: (history) => {
      const url = getUrlWithPrefix('/panel');
      history.push(url);
    },

    // ✅ CORRECCIÓN: Navegar a lista de entidad
    goToEntityList: (history, entitySlug) => {
      const url = getUrlWithPrefix(`/entity/${entitySlug}/list`);
      history.push(url);
    },

    // ✅ CORRECCIÓN: Navegar a formulario de entidad
    goToEntityForm: (history, entitySlug, docId = 'new') => {
      const url = getUrlWithPrefix(`/entity/${entitySlug}/${docId}/form`);
      history.push(url);
    },

    // ✅ CORRECCIÓN: Navegar a vista de entidad
    goToEntityShow: (history, entitySlug, docId) => {
      const url = getUrlWithPrefix(`/entity/${entitySlug}/${docId}`);
      history.push(url);
    },

    // ✅ CORRECCIÓN: Navegar a página de módulo
    goToModulePage: (history, entitySlug, action) => {
      const url = getUrlWithPrefix(`/${entitySlug}/${action}`);
      history.push(url);
    }
  };
};
