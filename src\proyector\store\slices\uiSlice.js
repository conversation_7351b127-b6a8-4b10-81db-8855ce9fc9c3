/**
 * UI Slice - Gestión de Estado de Interfaz
 * 
 * Maneja todo lo relacionado con:
 * - Estado de navegación y rutas
 * - Modales y overlays
 * - Layout y visualización
 * - Filtros y búsquedas
 * - Estados de carga y errores de UI
 */

import { useProyectorStore } from '../index';

/**
 * Crear slice de UI
 */
export const createUISlice = (set, get) => ({
  ui: {
    // Estado de navegación
    navigation: {
      currentRoute: null,
      previousRoute: null,
      breadcrumbs: [],
      isLoading: false,
      params: {}
    },
    
    // Estado de modales
    modals: {
      isFormOpen: false,
      isDeleteConfirmOpen: false,
      isFilterDrawerOpen: false,
      activeModal: null,
      modalData: null
    },

    // Estado del panel drawer lateral (NUEVO)
    panelDrawer: {
      isOpen: false,
      mode: null,           // 'filters', 'form', 'details', 'navigation'
      content: null,        // Contenido a mostrar
      entitySlug: null,     // Entidad relacionada
      docId: null,          // Documento relacionado
      title: '',            // Título del panel
      width: 'medium',      // 'small', 'medium', 'large', 'full'
      position: 'right',    // 'left', 'right'
      backdrop: true,       // Mostrar backdrop
      persistent: false,    // No cerrar al hacer click fuera
      loading: false,       // Estado de carga del contenido
      error: null           // Error del contenido
    },
    
    // Estado de filtros globales
    filters: {
      isDrawerOpen: false,
      isMobileFiltersOpen: false,
      activeFilters: {},
      searchQuery: ''
    },
    
    // Estado de layout
    layout: {
      menuVisible: true,
      sidebarCollapsed: false,
      filtersVisible: false,
      chatVisible: true, // Cambiar a true por defecto según big-picture
      listStyle: 'filter-paginated',
      theme: 'default',
      // MenuTop de 3 modos: harmony|site|user
      menuTopMode: 'site', // Modo por defecto: current site
      menuTopExpanded: false
    },
    
    // Estados de carga específicos de UI
    loading: {
      navigation: false,
      modal: false,
      filters: false,
      layout: false
    },
    
    // Errores de UI
    errors: {
      navigation: null,
      modal: null,
      filters: null,
      layout: null
    },

    // Acciones de navegación
    navigation: {
      setCurrentRoute: (route, params = {}) => {
        set((state) => ({
          ui: {
            ...state.ui,
            navigation: {
              ...state.ui.navigation,
              previousRoute: state.ui.navigation.currentRoute,
              currentRoute: route,
              params
            }
          }
        }));
      },

      setBreadcrumbs: (breadcrumbs) => {
        set((state) => ({
          ui: {
            ...state.ui,
            navigation: {
              ...state.ui.navigation,
              breadcrumbs
            }
          }
        }));
      },

      setNavigationLoading: (isLoading) => {
        set((state) => ({
          ui: {
            ...state.ui,
            navigation: {
              ...state.ui.navigation,
              isLoading
            },
            loading: {
              ...state.ui.loading,
              navigation: isLoading
            }
          }
        }));
      },

      setNavigationError: (error) => {
        set((state) => ({
          ui: {
            ...state.ui,
            errors: {
              ...state.ui.errors,
              navigation: error
            }
          }
        }));
      }
    },

    // Acciones de modales
    modals: {
      openModal: (modalType, data = null) => {
        set((state) => ({
          ui: {
            ...state.ui,
            modals: {
              ...state.ui.modals,
              activeModal: modalType,
              modalData: data,
              [modalType]: true
            }
          }
        }));
      },

      closeModal: (modalType = null) => {
        set((state) => {
          const newModals = { ...state.ui.modals };
          
          if (modalType) {
            newModals[modalType] = false;
            if (state.ui.modals.activeModal === modalType) {
              newModals.activeModal = null;
              newModals.modalData = null;
            }
          } else {
            // Cerrar todos los modales
            Object.keys(newModals).forEach(key => {
              if (typeof newModals[key] === 'boolean') {
                newModals[key] = false;
              }
            });
            newModals.activeModal = null;
            newModals.modalData = null;
          }
          
          return {
            ui: {
              ...state.ui,
              modals: newModals
            }
          };
        });
      },

      toggleModal: (modalType) => {
        const state = get();
        const isOpen = state.ui.modals[modalType];
        
        if (isOpen) {
          state.ui.modals.closeModal(modalType);
        } else {
          state.ui.modals.openModal(modalType);
        }
      }
    },

    // Acciones de filtros
    filters: {
      setDrawerOpen: (isOpen) => {
        set((state) => ({
          ui: {
            ...state.ui,
            filters: {
              ...state.ui.filters,
              isDrawerOpen: isOpen
            }
          }
        }));
      },

      setMobileFiltersOpen: (isOpen) => {
        set((state) => ({
          ui: {
            ...state.ui,
            filters: {
              ...state.ui.filters,
              isMobileFiltersOpen: isOpen
            }
          }
        }));
      },

      setActiveFilters: (filters) => {
        set((state) => ({
          ui: {
            ...state.ui,
            filters: {
              ...state.ui.filters,
              activeFilters: filters
            }
          }
        }));
      },

      setSearchQuery: (query) => {
        set((state) => ({
          ui: {
            ...state.ui,
            filters: {
              ...state.ui.filters,
              searchQuery: query
            }
          }
        }));
      },

      clearFilters: () => {
        set((state) => ({
          ui: {
            ...state.ui,
            filters: {
              ...state.ui.filters,
              activeFilters: {},
              searchQuery: ''
            }
          }
        }));
      }
    },

    // Acciones de layout
    layout: {
      setMenuVisible: (visible) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              menuVisible: visible
            }
          }
        }));
      },

      setSidebarCollapsed: (collapsed) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              sidebarCollapsed: collapsed
            }
          }
        }));
      },

      setFiltersVisible: (visible) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              filtersVisible: visible
            }
          }
        }));
      },

      setChatVisible: (visible) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              chatVisible: visible
            }
          }
        }));
      },

      setListStyle: (style) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              listStyle: style
            }
          }
        }));
      },

      setTheme: (theme) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              theme
            }
          }
        }));
      },

      toggleMenu: () => {
        const state = get();
        state.ui.layout.setMenuVisible(!state.ui.layout.menuVisible);
      },

      toggleSidebar: () => {
        const state = get();
        state.ui.layout.setSidebarCollapsed(!state.ui.layout.sidebarCollapsed);
      },

      toggleFilters: () => {
        const state = get();
        state.ui.layout.setFiltersVisible(!state.ui.layout.filtersVisible);
      },

      toggleChat: () => {
        const state = get();
        state.ui.layout.setChatVisible(!state.ui.layout.chatVisible);
      },

      // Acciones del MenuTop de 3 modos
      setMenuTopMode: (mode) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              menuTopMode: mode
            }
          }
        }));
      },

      setMenuTopExpanded: (expanded) => {
        set((state) => ({
          ui: {
            ...state.ui,
            layout: {
              ...state.ui.layout,
              menuTopExpanded: expanded
            }
          }
        }));
      },

      toggleMenuTop: () => {
        const state = get();
        state.ui.layout.setMenuTopExpanded(!state.ui.layout.menuTopExpanded);
      }
    },

    // Acciones de loading
    setLoading: (type, isLoading) => {
      set((state) => ({
        ui: {
          ...state.ui,
          loading: {
            ...state.ui.loading,
            [type]: isLoading
          }
        }
      }));
    },

    // Acciones de errores
    setError: (type, error) => {
      set((state) => ({
        ui: {
          ...state.ui,
          errors: {
            ...state.ui.errors,
            [type]: error
          }
        }
      }));
    },

    // Limpiar errores
    clearErrors: () => {
      set((state) => ({
        ui: {
          ...state.ui,
          errors: {
            navigation: null,
            modal: null,
            filters: null,
            layout: null
          }
        }
      }));
    },

    // === ACCIONES DEL PANEL DRAWER (NUEVAS) ===

    // Abrir panel drawer
    openPanelDrawer: (options = {}) => {
      const {
        mode = 'details',
        content = null,
        entitySlug = null,
        docId = null,
        title = '',
        width = 'medium',
        position = 'right',
        backdrop = true,
        persistent = false
      } = options;

      set((state) => ({
        ui: {
          ...state.ui,
          panelDrawer: {
            ...state.ui.panelDrawer,
            isOpen: true,
            mode,
            content,
            entitySlug,
            docId,
            title,
            width,
            position,
            backdrop,
            persistent,
            loading: false,
            error: null
          }
        }
      }));

      console.log(`[UI] Panel drawer abierto: ${mode} - ${title}`);
    },

    // Cerrar panel drawer
    closePanelDrawer: () => {
      set((state) => ({
        ui: {
          ...state.ui,
          panelDrawer: {
            ...state.ui.panelDrawer,
            isOpen: false,
            loading: false,
            error: null
          }
        }
      }));

      console.log('[UI] Panel drawer cerrado');
    },

    // Toggle panel drawer
    togglePanelDrawer: (options = {}) => {
      const state = get();

      if (state.ui.panelDrawer.isOpen) {
        state.ui.closePanelDrawer();
      } else {
        state.ui.openPanelDrawer(options);
      }
    },

    // Actualizar contenido del panel drawer
    updatePanelDrawerContent: (content, title = null) => {
      set((state) => ({
        ui: {
          ...state.ui,
          panelDrawer: {
            ...state.ui.panelDrawer,
            content,
            title: title || state.ui.panelDrawer.title,
            loading: false,
            error: null
          }
        }
      }));
    },

    // Establecer estado de carga del panel drawer
    setPanelDrawerLoading: (loading) => {
      set((state) => ({
        ui: {
          ...state.ui,
          panelDrawer: {
            ...state.ui.panelDrawer,
            loading
          }
        }
      }));
    },

    // Establecer error del panel drawer
    setPanelDrawerError: (error) => {
      set((state) => ({
        ui: {
          ...state.ui,
          panelDrawer: {
            ...state.ui.panelDrawer,
            error,
            loading: false
          }
        }
      }));
    },

    // Cambiar modo del panel drawer
    changePanelDrawerMode: (mode, options = {}) => {
      const state = get();

      // Si el panel está cerrado, abrirlo con el nuevo modo
      if (!state.ui.panelDrawer.isOpen) {
        state.ui.openPanelDrawer({ mode, ...options });
        return;
      }

      // Si está abierto, cambiar solo el modo y opciones
      set((prevState) => ({
        ui: {
          ...prevState.ui,
          panelDrawer: {
            ...prevState.ui.panelDrawer,
            mode,
            ...options,
            loading: false,
            error: null
          }
        }
      }));

      console.log(`[UI] Panel drawer modo cambiado a: ${mode}`);
    },

    /**
     * ========================================
     * FUNCIONALIDADES ROBUSTECIDAS
     * ========================================
     */

    /**
     * Navegación de breadcrumbs mejorada
     * REUTILIZA: navigation.setBreadcrumbs pero con funcionalidades avanzadas
     */
    breadcrumbs: {
      add: (breadcrumb) => {
        set((state) => ({
          ui: {
            ...state.ui,
            navigation: {
              ...state.ui.navigation,
              breadcrumbs: [...state.ui.navigation.breadcrumbs, breadcrumb]
            }
          }
        }));
      },

      remove: (index) => {
        set((state) => ({
          ui: {
            ...state.ui,
            navigation: {
              ...state.ui.navigation,
              breadcrumbs: state.ui.navigation.breadcrumbs.filter((_, i) => i !== index)
            }
          }
        }));
      },

      clear: () => {
        set((state) => ({
          ui: {
            ...state.ui,
            navigation: {
              ...state.ui.navigation,
              breadcrumbs: []
            }
          }
        }));
      },

      navigate: (index) => {
        const { ui } = get();
        const breadcrumb = ui.navigation.breadcrumbs[index];

        if (breadcrumb && breadcrumb.path) {
          ui.navigation.setCurrentRoute(breadcrumb.path);

          // Remover breadcrumbs posteriores
          set((state) => ({
            ui: {
              ...state.ui,
              navigation: {
                ...state.ui.navigation,
                breadcrumbs: state.ui.navigation.breadcrumbs.slice(0, index + 1)
              }
            }
          }));
        }
      }
    },

    /**
     * Historial de navegación
     * REUTILIZA: navigation.previousRoute pero con historial completo
     */
    history: {
      _stack: [],
      maxSize: 50,

      push: (route, params = {}) => {
        set((state) => {
          const newStack = [...state.ui.history._stack, { route, params, timestamp: Date.now() }];

          // Mantener tamaño máximo del historial
          if (newStack.length > state.ui.history.maxSize) {
            newStack.shift();
          }

          return {
            ui: {
              ...state.ui,
              history: {
                ...state.ui.history,
                _stack: newStack
              }
            }
          };
        });
      },

      back: () => {
        const { ui } = get();
        const stack = ui.history._stack;

        if (stack.length > 1) {
          const previousRoute = stack[stack.length - 2];
          ui.navigation.setCurrentRoute(previousRoute.route, previousRoute.params);

          // Remover la ruta actual del historial
          set((state) => ({
            ui: {
              ...state.ui,
              history: {
                ...state.ui.history,
                _stack: state.ui.history._stack.slice(0, -1)
              }
            }
          }));
        }
      },

      forward: () => {
        // Implementación básica - podría expandirse con un stack de "forward"
        console.log('[UI] Forward navigation not implemented yet');
      },

      clear: () => {
        set((state) => ({
          ui: {
            ...state.ui,
            history: {
              ...state.ui.history,
              _stack: []
            }
          }
        }));
      },

      getStack: () => {
        const { ui } = get();
        return ui.history._stack;
      }
    },

    /**
     * Guardias de rutas
     * REUTILIZA: sitesSlice.sitePermissions para verificación de acceso
     */
    routeGuards: {
      _guards: {},

      register: (routePattern, guardFunction) => {
        set((state) => ({
          ui: {
            ...state.ui,
            routeGuards: {
              ...state.ui.routeGuards,
              _guards: {
                ...state.ui.routeGuards._guards,
                [routePattern]: guardFunction
              }
            }
          }
        }));
      },

      check: async (route, params = {}) => {
        const { ui, sites, auth } = get();
        const guards = ui.routeGuards._guards;

        for (const [pattern, guardFunction] of Object.entries(guards)) {
          const regex = new RegExp(pattern.replace(/:\w+/g, '([^/]+)'));

          if (regex.test(route)) {
            try {
              const canAccess = await guardFunction(route, params, { sites, auth });

              if (!canAccess) {
                return {
                  allowed: false,
                  reason: 'route_guard_failed',
                  redirectTo: '/unauthorized'
                };
              }
            } catch (error) {
              console.error('[UI] Route guard error:', error);
              return {
                allowed: false,
                reason: 'route_guard_error',
                redirectTo: '/error'
              };
            }
          }
        }

        return { allowed: true };
      },

      unregister: (routePattern) => {
        set((state) => ({
          ui: {
            ...state.ui,
            routeGuards: {
              ...state.ui.routeGuards,
              _guards: {
                ...state.ui.routeGuards._guards,
                [routePattern]: undefined
              }
            }
          }
        }));
      }
    },

    /**
     * Sistema de notificaciones toast
     * REUTILIZA: modals patterns pero para notificaciones temporales
     */
    toastNotifications: {
      _toasts: [],
      _nextId: 1,

      show: (notification) => {
        const {
          message,
          type = 'info',
          duration = 5000,
          persistent = false,
          actions = []
        } = notification;

        const toast = {
          id: get().ui.toastNotifications._nextId,
          message,
          type,
          duration,
          persistent,
          actions,
          createdAt: Date.now()
        };

        set((state) => ({
          ui: {
            ...state.ui,
            toastNotifications: {
              ...state.ui.toastNotifications,
              _toasts: [...state.ui.toastNotifications._toasts, toast],
              _nextId: state.ui.toastNotifications._nextId + 1
            }
          }
        }));

        // Auto-remover si no es persistente
        if (!persistent) {
          setTimeout(() => {
            get().ui.toastNotifications.remove(toast.id);
          }, duration);
        }

        return toast.id;
      },

      remove: (toastId) => {
        set((state) => ({
          ui: {
            ...state.ui,
            toastNotifications: {
              ...state.ui.toastNotifications,
              _toasts: state.ui.toastNotifications._toasts.filter(toast => toast.id !== toastId)
            }
          }
        }));
      },

      clear: () => {
        set((state) => ({
          ui: {
            ...state.ui,
            toastNotifications: {
              ...state.ui.toastNotifications,
              _toasts: []
            }
          }
        }));
      },

      success: (message, options = {}) => {
        return get().ui.toastNotifications.show({
          message,
          type: 'success',
          ...options
        });
      },

      error: (message, options = {}) => {
        return get().ui.toastNotifications.show({
          message,
          type: 'error',
          persistent: true,
          ...options
        });
      },

      warning: (message, options = {}) => {
        return get().ui.toastNotifications.show({
          message,
          type: 'warning',
          ...options
        });
      },

      info: (message, options = {}) => {
        return get().ui.toastNotifications.show({
          message,
          type: 'info',
          ...options
        });
      }
    },

    /**
     * Gestión de temas mejorada
     * REUTILIZA: sitesSlice.siteBranding para aplicar temas del site
     */
    themeManagement: {
      applyTheme: async (theme, siteId = null) => {
        try {
          // REUTILIZAR: sitesSlice.siteBranding si se proporciona siteId
          if (siteId) {
            const { sites } = get();
            const brandingResult = await sites.siteBranding.load(siteId);

            if (brandingResult.success) {
              // Aplicar branding del site como tema
              sites.siteBranding.applyBranding(brandingResult.branding);
            }
          }

          // Aplicar tema local
          set((state) => ({
            ui: {
              ...state.ui,
              layout: {
                ...state.ui.layout,
                theme
              }
            }
          }));

          // Aplicar clases CSS del tema
          document.documentElement.className = `theme-${theme}`;

          return { success: true, theme };

        } catch (error) {
          console.error('[UI] Error applying theme:', error);
          return { success: false, error: error.message };
        }
      },

      getAvailableThemes: () => {
        return [
          { id: 'default', name: 'Default', description: 'Tema por defecto' },
          { id: 'dark', name: 'Dark', description: 'Tema oscuro' },
          { id: 'light', name: 'Light', description: 'Tema claro' },
          { id: 'auto', name: 'Auto', description: 'Automático según sistema' }
        ];
      },

      detectSystemTheme: () => {
        if (typeof window !== 'undefined' && window.matchMedia) {
          return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        return 'light';
      }
    },

    /**
     * Estados adicionales para funcionalidades nuevas
     */
    history: {
      _stack: [],
      maxSize: 50
    },

    routeGuards: {
      _guards: {}
    },

    toastNotifications: {
      _toasts: [],
      _nextId: 1
    }
  }
});

/**
 * Hook específico para UI mejorado
 */
export const useUI = () => {
  const store = useProyectorStore();
  return {
    // Estado existente
    navigation: store.ui.navigation,
    modals: store.ui.modals,
    filters: store.ui.filters,
    layout: store.ui.layout,
    loading: store.ui.loading,
    errors: store.ui.errors,

    // Nuevo estado del panel drawer
    panelDrawer: store.ui.panelDrawer,

    // Acciones existentes
    ...store.ui.navigation,
    ...store.ui.modals,
    ...store.ui.filters,
    ...store.ui.layout,
    setLoading: store.ui.setLoading,
    setError: store.ui.setError,
    clearErrors: store.ui.clearErrors,

    // Nuevas acciones del panel drawer
    openPanelDrawer: store.ui.openPanelDrawer,
    closePanelDrawer: store.ui.closePanelDrawer,
    togglePanelDrawer: store.ui.togglePanelDrawer,
    updatePanelDrawerContent: store.ui.updatePanelDrawerContent,
    setPanelDrawerLoading: store.ui.setPanelDrawerLoading,
    setPanelDrawerError: store.ui.setPanelDrawerError,
    changePanelDrawerMode: store.ui.changePanelDrawerMode
  };
};

/**
 * Hook específico para panel drawer
 */
export const usePanelDrawer = () => {
  const store = useProyectorStore();
  return {
    // Estado del panel drawer
    ...store.ui.panelDrawer,

    // Acciones del panel drawer
    open: store.ui.openPanelDrawer,
    close: store.ui.closePanelDrawer,
    toggle: store.ui.togglePanelDrawer,
    updateContent: store.ui.updatePanelDrawerContent,
    setLoading: store.ui.setPanelDrawerLoading,
    setError: store.ui.setPanelDrawerError,
    changeMode: store.ui.changePanelDrawerMode
  };
};
